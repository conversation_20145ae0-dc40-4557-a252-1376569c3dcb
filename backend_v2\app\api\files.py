"""
文件管理API
"""
from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, status, UploadFile, File
from fastapi.responses import StreamingResponse
from sqlalchemy.orm import Session
import uuid
import os
from datetime import timedelta, datetime

from app.core.database import get_db
from app.services.minio_service import get_minio_service, MinIOService
from app.services.file_metadata_service import FileMetadataService
from app.services.file_usage_service import FileUsageService
from app.services.batch_download_service import BatchDownloadService
from app.core.config import settings
from app.utils.storage_path import StoragePathManager, StorageType
from app.crud.crud_file import file as file_crud
from app.schemas.file import FileCreate
from app.models.file import FileType, FileStatus

router = APIRouter(prefix="/files", tags=["Files"])


@router.post("/presign")
async def get_presigned_upload_url(
    filename: str,
    file_type: str,
    expires_hours: int = 1,
    db: Session = Depends(get_db),
    minio_service: MinIOService = Depends(get_minio_service)
):
    """
    获取预签名上传URL
    """
    # 验证文件类型
    allowed_extensions = settings.get_allowed_image_extensions() + settings.get_allowed_video_extensions()
    file_ext = os.path.splitext(filename)[1].lower().lstrip('.')

    if file_ext not in allowed_extensions:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"File type .{file_ext} not allowed. Allowed types: {', '.join(allowed_extensions)}"
        )

    # 验证过期时间
    if expires_hours < 1 or expires_hours > 24:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Expires hours must be between 1 and 24"
        )

    try:
        # 确保MinIO服务已初始化
        if not minio_service.initialize():
            raise HTTPException(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                detail="Storage service unavailable"
            )

        # 使用新的路径管理器生成存储路径
        object_name, file_id = StoragePathManager.generate_file_path(
            filename=filename,
            storage_type=StorageType.UPLOAD
        )

        # 生成预签名上传URL
        expires = timedelta(hours=expires_hours)
        presigned_url = minio_service.generate_presigned_put_url(
            object_name=object_name,
            expires=expires
        )

        if not presigned_url:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to generate presigned upload URL"
            )

        # 保存文件元数据到数据库
        file_category = StoragePathManager.get_file_category(filename)

        # 确定文件类型
        if file_category.value == "images":
            db_file_type = FileType.IMAGE
        elif file_category.value == "videos":
            db_file_type = FileType.VIDEO
        elif file_category.value == "audio":
            db_file_type = FileType.AUDIO
        elif file_category.value == "documents":
            db_file_type = FileType.DOCUMENT
        else:
            db_file_type = FileType.OTHER

        # 创建文件记录
        file_create = FileCreate(
            filename=filename,
            original_filename=filename,
            file_type=db_file_type,
            content_type=file_type,
            storage_path=object_name,
            storage_bucket=minio_service.bucket_name,
            file_size=None,  # 预签名URL时还不知道文件大小
            batch_id=None,  # 预签名URL不属于任何批次
            file_metadata={
                "presigned_url_generated": True,
                "expires_at": (datetime.now() + expires).isoformat(),
                "upload_method": "presigned_url"
            }
        )

        # 保存到数据库
        db_file = file_crud.create(db=db, obj_in=file_create)

        return {
            "file_id": file_id,
            "database_id": db_file.id,
            "object_name": object_name,
            "presigned_url": presigned_url,
            "expires_in": int(expires.total_seconds()),
            "method": "PUT",
            "headers": {
                "Content-Type": file_type
            },
            "instructions": {
                "method": "PUT",
                "url": presigned_url,
                "headers": {"Content-Type": file_type},
                "body": "Binary file content"
            }
        }

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to generate presigned URL: {str(e)}"
        )


@router.post("/upload")
async def upload_file(
    file: UploadFile = File(...),
    db: Session = Depends(get_db),
    minio_service: MinIOService = Depends(get_minio_service)
):
    """
    直接上传文件（小文件）
    """
    # 验证文件类型
    allowed_extensions = settings.get_allowed_image_extensions() + settings.get_allowed_video_extensions()
    filename = file.filename or "unknown"
    file_ext = os.path.splitext(filename)[1].lower().lstrip('.')

    if file_ext not in allowed_extensions:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"File type .{file_ext} not allowed. Allowed types: {', '.join(allowed_extensions)}"
        )

    # 验证文件大小（这里设置为100MB限制）
    max_size = 100 * 1024 * 1024  # 100MB
    if file.size and file.size > max_size:
        raise HTTPException(
            status_code=status.HTTP_413_REQUEST_ENTITY_TOO_LARGE,
            detail=f"File size {file.size} exceeds maximum allowed size {max_size}"
        )

    # 使用新的路径管理器生成存储路径
    object_name, file_id = StoragePathManager.generate_file_path(
        filename=filename,
        storage_type=StorageType.UPLOAD
    )

    try:
        # 确保MinIO服务已初始化
        if not minio_service.initialize():
            raise HTTPException(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                detail="Storage service unavailable"
            )

        # 读取文件内容并计算校验和
        file_content = file.file.read()
        file.file.seek(0)  # 重置文件指针

        checksum = FileMetadataService.calculate_file_checksum(file_content)

        # 上传文件到MinIO
        success = minio_service.upload_file(
            file_data=file.file,
            object_name=object_name,
            content_type=file.content_type,
            metadata={
                "file_id": file_id,
                "original_filename": filename,
                "upload_timestamp": str(uuid.uuid1().time),
                "checksum": checksum
            }
        )

        if not success:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to upload file to storage"
            )

        # 获取文件信息验证上传成功
        file_info = minio_service.get_file_info(object_name)
        if not file_info:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="File upload verification failed"
            )

        # 保存文件元数据到数据库
        filename = file.filename or "unknown"
        file_category = StoragePathManager.get_file_category(filename)

        # 确定文件类型
        if file_category.value == "images":
            db_file_type = FileType.IMAGE
        elif file_category.value == "videos":
            db_file_type = FileType.VIDEO
        elif file_category.value == "audio":
            db_file_type = FileType.AUDIO
        elif file_category.value == "documents":
            db_file_type = FileType.DOCUMENT
        else:
            db_file_type = FileType.OTHER

        # 创建文件记录
        file_create = FileCreate(
            filename=filename,
            original_filename=filename,
            file_type=db_file_type,
            content_type=file.content_type,
            storage_path=object_name,
            storage_bucket=minio_service.bucket_name,
            file_size=file_info.get("size", file.size),
            batch_id=None,  # 直接上传不属于任何批次
            file_metadata={
                "upload_method": "direct_upload",
                "etag": file_info.get("etag"),
                "last_modified": file_info.get("last_modified"),
                "upload_timestamp": str(uuid.uuid1().time),
                "checksum_algorithm": "sha256"
            }
        )

        # 保存到数据库
        db_file = file_crud.create(db=db, obj_in=file_create)

        # 更新状态和校验和
        from app.schemas.file import FileUpdate
        file_update = FileUpdate(
            status=FileStatus.UPLOADED,
            checksum=checksum
        )
        db_file = file_crud.update(db=db, db_obj=db_file, obj_in=file_update)

        return {
            "file_id": file_id,
            "database_id": db_file.id,
            "filename": file.filename,
            "size": file_info.get("size", file.size),
            "content_type": file.content_type,
            "object_name": object_name,
            "status": "uploaded",
            "etag": file_info.get("etag"),
            "last_modified": file_info.get("last_modified")
        }

    except Exception as e:
        # 如果是已知的HTTP异常，直接抛出
        if isinstance(e, HTTPException):
            raise e

        # 其他异常转换为500错误
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Upload failed: {str(e)}"
        )


@router.get("/")
async def list_files(
    skip: int = 0,
    limit: int = 100,
    file_type: Optional[str] = None,
    db: Session = Depends(get_db)
):
    """
    获取文件列表
    """
    # 这里应该从数据库查询用户的文件
    files = [
        {
            "id": "file_1",
            "filename": "image1.jpg",
            "size": 1024000,
            "content_type": "image/jpeg",
            "status": "uploaded",
            "created_at": "2025-01-05T00:00:00Z"
        },
        {
            "id": "file_2",
            "filename": "video1.mp4", 
            "size": 5120000,
            "content_type": "video/mp4",
            "status": "uploaded",
            "created_at": "2025-01-05T01:00:00Z"
        }
    ]
    
    return {
        "files": files,
        "total": len(files),
        "skip": skip,
        "limit": limit
    }


@router.get("/{file_id}")
async def get_file_info(
    file_id: str,
    filename: Optional[str] = None,
    db: Session = Depends(get_db),
    minio_service: MinIOService = Depends(get_minio_service)
):
    """
    获取文件信息
    """
    try:
        # 确保MinIO服务已初始化
        if not minio_service.initialize():
            raise HTTPException(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                detail="Storage service unavailable"
            )

        # 如果没有提供文件名，返回错误
        # 实际应用中应该从数据库查询文件名
        if not filename:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Filename parameter required"
            )

        object_name = f"uploads/{file_id}/{filename}"

        # 检查文件是否存在
        if not minio_service.file_exists(object_name):
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"File {file_id} not found"
            )

        # 获取文件信息
        file_info = minio_service.get_file_info(object_name)
        if not file_info:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"File {file_id} metadata not found"
            )

        return {
            "id": file_id,
            "filename": filename,
            "object_name": object_name,
            "size": file_info.get("size"),
            "content_type": file_info.get("content_type"),
            "etag": file_info.get("etag"),
            "last_modified": file_info.get("last_modified"),
            "metadata": file_info.get("metadata", {}),
            "status": "uploaded"
        }

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get file info: {str(e)}"
        )


@router.get("/{file_id}/download")
async def download_file(
    file_id: str,
    filename: Optional[str] = None,
    db: Session = Depends(get_db),
    minio_service: MinIOService = Depends(get_minio_service)
):
    """
    下载文件 - 使用302重定向到预签名URL
    """
    try:
        # 确保MinIO服务已初始化
        if not minio_service.initialize():
            raise HTTPException(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                detail="Storage service unavailable"
            )

        # 首先尝试从数据库查询文件信息
        db_file = None

        # 如果file_id是数据库ID（数字），直接查询
        if file_id.isdigit():
            db_file = file_crud.get(db=db, id=int(file_id))
        else:
            # 如果是UUID格式，通过文件元数据查询
            # 这里需要实现通过storage_path查询的方法
            # 暂时使用传统方式
            pass

        # 确定对象名称
        if db_file:
            object_name = str(db_file.storage_path)
            filename = filename or str(db_file.filename)
        else:
            # 回退到传统方式
            if not filename:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Filename parameter required for download when file not found in database"
                )
            object_name = f"uploads/{file_id}/{filename}"

        # 检查文件是否存在
        if not minio_service.file_exists(object_name):
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"File {file_id} not found in storage"
            )

        # 生成预签名下载URL（有效期1小时）
        expires = timedelta(hours=1)
        presigned_url = minio_service.generate_presigned_get_url(
            object_name=object_name,
            expires=expires
        )

        if not presigned_url:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to generate download URL"
            )

        # 记录文件访问统计
        if db_file:
            file_id_int = getattr(db_file, 'id', None)
            if file_id_int is not None:
                FileUsageService.record_file_access(db=db, file_id=file_id_int, access_type="download")

        # 返回302重定向到预签名URL
        from fastapi.responses import RedirectResponse
        return RedirectResponse(url=presigned_url, status_code=302)

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Download failed: {str(e)}"
        )


@router.get("/{file_id}/thumbnail")
async def get_thumbnail(
    file_id: str,
    size: str = "medium",  # small, medium, large
    db: Session = Depends(get_db)
):
    """
    获取文件缩略图
    """
    # 这里应该从MinIO获取缩略图
    # 暂时返回模拟响应
    
    def generate_thumbnail():
        yield b"fake thumbnail content"
    
    return StreamingResponse(
        generate_thumbnail(),
        media_type="image/jpeg"
    )


@router.delete("/{file_id}")
async def delete_file(
    file_id: str,
    db: Session = Depends(get_db)
):
    """
    删除文件
    """
    # 这里应该从MinIO和数据库删除文件
    return {"message": f"File {file_id} deleted successfully"}


@router.post("/batch-download")
async def batch_download(
    file_ids: List[str],
    db: Session = Depends(get_db)
):
    """
    批量下载文件（生成ZIP）
    """
    # 这里应该创建ZIP文件并返回下载链接
    download_id = str(uuid.uuid4())
    
    return {
        "download_id": download_id,
        "download_url": f"/files/download-zip/{download_id}",
        "expires_in": 3600,
        "file_count": len(file_ids)
    }


@router.post("/download-zip")
async def create_batch_download(
    file_ids: List[int],
    db: Session = Depends(get_db),
    minio_service: MinIOService = Depends(get_minio_service)
):
    """
    创建批量下载ZIP文件
    """
    try:
        # 验证下载权限
        has_permission, error_msg, valid_files = BatchDownloadService.validate_download_permission(
            db=db, file_ids=file_ids
        )

        if not has_permission:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=error_msg
            )

        if not valid_files:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="No valid files found for download"
            )

        # 获取下载统计信息
        stats = BatchDownloadService.get_download_statistics(db=db, file_ids=file_ids)

        # 记录批量下载统计
        BatchDownloadService.record_batch_download(db=db, file_ids=file_ids)

        # 生成ZIP文件名
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        zip_filename = f"batch_download_{timestamp}_{len(valid_files)}files.zip"

        # 创建流式ZIP响应
        zip_generator = BatchDownloadService.create_streaming_zip(
            files=valid_files,
            minio_service=minio_service
        )

        return StreamingResponse(
            zip_generator,
            media_type="application/zip",
            headers={
                "Content-Disposition": f"attachment; filename={zip_filename}",
                "X-File-Count": str(len(valid_files)),
                "X-Total-Size": str(stats["total_size_bytes"])
            }
        )

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create batch download: {str(e)}"
        )


@router.get("/download/{result_id}")
async def download_result_files(
    result_id: str,
    format: str = "zip",  # zip 或 redirect
    db: Session = Depends(get_db),
    minio_service: MinIOService = Depends(get_minio_service)
):
    """
    下载结果文件 - 支持任务或批次的结果下载

    Args:
        result_id: 结果ID（task_id或batch_id）
        format: 下载格式（zip=打包下载，redirect=重定向到单文件）
    """
    try:
        # 验证结果下载权限
        has_permission, error_msg, result_files = BatchDownloadService.validate_result_download_permission(
            db=db, result_id=result_id
        )

        if not has_permission:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=error_msg
            )

        if not result_files:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"No result files found for {result_id}"
            )

        # 记录下载统计
        file_ids = [getattr(f, 'id', 0) for f in result_files]
        BatchDownloadService.record_batch_download(
            db=db,
            file_ids=file_ids,
            download_type="result_download"
        )

        # 如果只有一个文件且要求重定向，直接重定向
        if len(result_files) == 1 and format == "redirect":
            file_obj = result_files[0]

            # 生成预签名下载URL
            expires = timedelta(hours=1)
            presigned_url = minio_service.generate_presigned_get_url(
                object_name=str(file_obj.storage_path),
                expires=expires
            )

            if not presigned_url:
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail="Failed to generate download URL"
                )

            # 返回302重定向
            from fastapi.responses import RedirectResponse
            return RedirectResponse(url=presigned_url, status_code=302)

        # 否则创建ZIP下载
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        zip_filename = f"result_{result_id}_{timestamp}_{len(result_files)}files.zip"

        # 创建流式ZIP响应
        zip_generator = BatchDownloadService.create_streaming_zip(
            files=result_files,
            minio_service=minio_service
        )

        return StreamingResponse(
            zip_generator,
            media_type="application/zip",
            headers={
                "Content-Disposition": f"attachment; filename={zip_filename}",
                "X-Result-ID": result_id,
                "X-File-Count": str(len(result_files))
            }
        )

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to download result files: {str(e)}"
        )


@router.get("/{file_id}/presign-download")
async def get_presigned_download_url(
    file_id: str,
    filename: str,
    expires_hours: int = 1,
    db: Session = Depends(get_db),
    minio_service: MinIOService = Depends(get_minio_service)
):
    """
    获取预签名下载URL
    """
    # 验证过期时间
    if expires_hours < 1 or expires_hours > 24:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Expires hours must be between 1 and 24"
        )

    try:
        # 确保MinIO服务已初始化
        if not minio_service.initialize():
            raise HTTPException(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                detail="Storage service unavailable"
            )

        # 构造对象名称
        object_name = f"uploads/{file_id}/{filename}"

        # 检查文件是否存在
        if not minio_service.file_exists(object_name):
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"File {file_id} not found"
            )

        # 生成预签名下载URL
        expires = timedelta(hours=expires_hours)
        presigned_url = minio_service.generate_presigned_get_url(
            object_name=object_name,
            expires=expires
        )

        if not presigned_url:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to generate presigned download URL"
            )

        # 获取文件信息
        file_info = minio_service.get_file_info(object_name)

        return {
            "file_id": file_id,
            "filename": filename,
            "object_name": object_name,
            "presigned_url": presigned_url,
            "expires_in": int(expires.total_seconds()),
            "method": "GET",
            "file_info": {
                "size": file_info.get("size") if file_info else None,
                "content_type": file_info.get("content_type") if file_info else None,
                "last_modified": file_info.get("last_modified") if file_info else None
            }
        }

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to generate presigned download URL: {str(e)}"
        )


@router.get("/storage/statistics")
async def get_storage_statistics(
    minio_service: MinIOService = Depends(get_minio_service)
):
    """
    获取存储统计信息
    """
    try:
        # 确保MinIO服务已初始化
        if not minio_service.initialize():
            raise HTTPException(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                detail="Storage service unavailable"
            )

        stats = minio_service.get_storage_statistics()

        if "error" in stats:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Failed to get storage statistics: {stats['error']}"
            )

        return stats

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get storage statistics: {str(e)}"
        )


@router.get("/storage/directory")
async def list_storage_directory(
    prefix: str = "",
    recursive: bool = False,
    minio_service: MinIOService = Depends(get_minio_service)
):
    """
    列出存储目录内容
    """
    try:
        # 确保MinIO服务已初始化
        if not minio_service.initialize():
            raise HTTPException(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                detail="Storage service unavailable"
            )

        files = minio_service.list_directory(prefix=prefix, recursive=recursive)

        return {
            "prefix": prefix,
            "recursive": recursive,
            "files": files,
            "count": len(files)
        }

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to list directory: {str(e)}"
        )


@router.post("/storage/ensure-structure")
async def ensure_storage_structure(
    minio_service: MinIOService = Depends(get_minio_service)
):
    """
    确保存储桶和目录结构存在
    """
    try:
        # 确保MinIO服务已初始化
        if not minio_service.initialize():
            raise HTTPException(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                detail="Storage service unavailable"
            )

        success = minio_service.ensure_bucket_structure()

        if not success:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to ensure storage structure"
            )

        return {
            "message": "Storage structure ensured successfully",
            "bucket_name": minio_service.bucket_name
        }

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to ensure storage structure: {str(e)}"
        )


@router.get("/{file_id}/duplicates")
async def find_file_duplicates(
    file_id: int,
    db: Session = Depends(get_db)
):
    """
    查找文件的重复项
    """
    try:
        # 获取原文件
        original_file = file_crud.get(db=db, id=file_id)
        if not original_file:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="File not found"
            )

        duplicates = []

        # 通过校验和查找重复
        if original_file.checksum is not None:
            checksum_duplicates = file_crud.find_duplicates_by_checksum(
                db=db, checksum=str(original_file.checksum)
            )
            duplicates.extend([f for f in checksum_duplicates if f.id != file_id])

        # 通过文件名和大小查找可能重复
        filename = getattr(original_file, 'filename', None)
        file_size = getattr(original_file, 'file_size', None)
        if filename is not None and file_size is not None:
            name_size_duplicates = file_crud.find_duplicates_by_name_and_size(
                db=db, filename=filename, file_size=file_size
            )
            # 去重并排除原文件
            for dup in name_size_duplicates:
                if getattr(dup, 'id', None) != file_id and dup not in duplicates:
                    duplicates.append(dup)

        return {
            "original_file": {
                "id": original_file.id,
                "filename": original_file.filename,
                "file_size": original_file.file_size,
                "checksum": original_file.checksum
            },
            "duplicates": [
                {
                    "id": dup.id,
                    "filename": dup.filename,
                    "file_size": dup.file_size,
                    "checksum": dup.checksum,
                    "created_at": dup.created_at,
                    "match_type": "checksum" if dup.checksum == original_file.checksum else "name_size"
                }
                for dup in duplicates
            ],
            "duplicate_count": len(duplicates)
        }

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to find duplicates: {str(e)}"
        )


@router.get("/{original_filename}/versions")
async def get_file_versions(
    original_filename: str,
    batch_id: Optional[int] = None,
    db: Session = Depends(get_db)
):
    """
    获取文件的所有版本
    """
    try:
        versions = file_crud.get_file_versions(
            db=db, original_filename=original_filename, batch_id=batch_id
        )

        return {
            "original_filename": original_filename,
            "batch_id": batch_id,
            "version_count": len(versions),
            "versions": [
                {
                    "id": version.id,
                    "filename": version.filename,
                    "file_size": version.file_size,
                    "status": version.status,
                    "created_at": version.created_at,
                    "version_metadata": getattr(version, 'file_metadata', {}).get("version_number") if getattr(version, 'file_metadata', None) is not None else None,
                    "is_latest": getattr(version, 'file_metadata', {}).get("is_latest_version", False) if getattr(version, 'file_metadata', None) is not None else False
                }
                for version in versions
            ]
        }

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get file versions: {str(e)}"
        )


@router.get("/metadata/statistics")
async def get_file_statistics(
    file_type: Optional[str] = None,
    db: Session = Depends(get_db)
):
    """
    获取文件统计信息
    """
    try:
        # 转换文件类型
        parsed_file_type = None
        if file_type:
            try:
                parsed_file_type = FileType(file_type.lower())
            except ValueError:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"Invalid file type: {file_type}"
                )

        stats = FileMetadataService.get_file_statistics(db=db, file_type=parsed_file_type)
        return stats

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get statistics: {str(e)}"
        )


@router.get("/lifecycle/statistics")
async def get_lifecycle_statistics(
    db: Session = Depends(get_db)
):
    """
    获取文件生命周期统计信息
    """
    try:
        from app.services.file_lifecycle_service import FileLifecycleService
        stats = FileLifecycleService.get_lifecycle_statistics(db=db)
        return stats

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get lifecycle statistics: {str(e)}"
        )


@router.post("/lifecycle/cleanup")
async def trigger_file_cleanup(
    dry_run: bool = True
):
    """
    手动触发文件清理任务
    """
    try:
        from app.core.celery_app import celery_app

        # 异步执行清理任务
        task = celery_app.send_task(
            'app.tasks.lifecycle_tasks.cleanup_expired_files',
            args=[dry_run]
        )

        return {
            "task_id": task.id,
            "status": "started",
            "dry_run": dry_run,
            "message": f"File cleanup task started ({'dry run' if dry_run else 'real cleanup'})"
        }

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to start cleanup task: {str(e)}"
        )


@router.post("/lifecycle/archive")
async def trigger_file_archival(
    archive_days: int = 365
):
    """
    手动触发文件归档任务
    """
    try:
        from app.core.celery_app import celery_app

        # 异步执行归档任务
        task = celery_app.send_task(
            'app.tasks.lifecycle_tasks.archive_old_files',
            args=[archive_days]
        )

        return {
            "task_id": task.id,
            "status": "started",
            "archive_days": archive_days,
            "message": f"File archival task started (files older than {archive_days} days)"
        }

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to start archival task: {str(e)}"
        )


@router.get("/lifecycle/expired")
async def get_expired_files(
    limit: int = 50,
    db: Session = Depends(get_db)
):
    """
    获取过期文件列表
    """
    try:
        from app.services.file_lifecycle_service import FileLifecycleService

        expired_files = FileLifecycleService.find_expired_files(db=db, limit=limit)

        return {
            "total_found": len(expired_files),
            "limit": limit,
            "expired_files": [
                {
                    "id": file_obj.id,
                    "filename": getattr(file_obj, 'filename', 'unknown'),
                    "storage_path": getattr(file_obj, 'storage_path', ''),
                    "created_at": getattr(file_obj, 'created_at', None),
                    "file_size": getattr(file_obj, 'file_size', 0),
                    "policy": FileLifecycleService.get_file_policy(getattr(file_obj, 'storage_path', '')).value,
                    "days_expired": (datetime.now() - getattr(file_obj, 'created_at', datetime.now())).days
                }
                for file_obj in expired_files
            ]
        }

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get expired files: {str(e)}"
        )


@router.get("/{file_id}/usage")
async def get_file_usage_stats(
    file_id: int,
    db: Session = Depends(get_db)
):
    """
    获取文件使用统计
    """
    try:
        stats = FileUsageService.get_file_usage_stats(db=db, file_id=file_id)

        if stats is None:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="File not found"
            )

        return stats

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get usage stats: {str(e)}"
        )


@router.get("/usage/popular")
async def get_popular_files(
    limit: int = 10,
    days: int = 30,
    db: Session = Depends(get_db)
):
    """
    获取热门文件列表
    """
    try:
        popular_files = FileUsageService.get_popular_files(db=db, limit=limit, days=days)

        return {
            "limit": limit,
            "days": days,
            "popular_files": popular_files,
            "total_found": len(popular_files)
        }

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get popular files: {str(e)}"
        )


@router.get("/usage/unused")
async def get_unused_files(
    days: int = 90,
    limit: int = 100,
    db: Session = Depends(get_db)
):
    """
    获取未使用的文件列表
    """
    try:
        unused_files = FileUsageService.get_unused_files(db=db, days=days, limit=limit)

        return {
            "days_threshold": days,
            "limit": limit,
            "unused_files": unused_files,
            "total_found": len(unused_files)
        }

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get unused files: {str(e)}"
        )


@router.get("/usage/summary")
async def get_usage_summary(
    db: Session = Depends(get_db)
):
    """
    获取文件使用情况摘要
    """
    try:
        summary = FileUsageService.get_usage_summary(db=db)
        return summary

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get usage summary: {str(e)}"
        )


@router.post("/download/statistics")
async def get_download_statistics(
    file_ids: List[int],
    db: Session = Depends(get_db)
):
    """
    获取下载统计信息
    """
    try:
        stats = BatchDownloadService.get_download_statistics(db=db, file_ids=file_ids)
        return stats

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get download statistics: {str(e)}"
        )
