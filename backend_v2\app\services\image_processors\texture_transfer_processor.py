"""
纹理迁移处理器
实现基于块匹配的纹理迁移功能，将源纹理应用到目标图像
"""
from typing import Dict, Any, List, Optional
import cv2
import numpy as np
import structlog
from sqlalchemy.orm import Session

from app.services.image_processor_base import ImageProcessorBase, ParameterConverter
from app.services.minio_service import MinIOService
from app.models.file import File

logger = structlog.get_logger()


class TextureTransferProcessor(ImageProcessorBase):
    """纹理迁移处理器 - 处理双文件输入"""
    
    def process_multiple_images(
        self, 
        file_objects: List[File], 
        parameters: Dict[str, Any],
        operation_name: str,
        task_id: str = None
    ) -> Dict[str, Any]:
        """
        处理纹理迁移
        
        Args:
            file_objects: 文件对象列表（应该包含2个文件：目标图像和源纹理）
            parameters: 处理参数
            operation_name: 操作名称
            task_id: 任务ID
            
        Returns:
            处理结果字典
        """
        import time
        start_time = time.time()
        
        try:
            if len(file_objects) != 2:
                raise ValueError(f"Texture transfer requires exactly 2 images (target and source texture), got {len(file_objects)}")
            
            # 更新进度：开始下载
            if task_id:
                self.update_task_progress(task_id, 10, 100, "Downloading images...")
            
            # 下载目标图像和源纹理
            target_image, temp_path1 = self.download_image_from_minio(file_objects[0])
            source_texture, temp_path2 = self.download_image_from_minio(file_objects[1])
            
            # 更新进度：开始处理
            if task_id:
                self.update_task_progress(task_id, 30, 100, "Processing texture transfer...")
            
            # 处理纹理迁移
            result_image = self.process_texture_transfer(target_image, source_texture, parameters)
            
            # 更新进度：开始上传
            if task_id:
                self.update_task_progress(task_id, 80, 100, "Uploading result...")
            
            # 计算处理时间
            processing_time = time.time() - start_time
            
            # 上传结果（使用目标图像作为基础信息）
            result = self.upload_processed_image(
                processed_image=result_image,
                original_file=file_objects[0],
                operation_name=operation_name,
                parameters=parameters,
                processing_time=processing_time
            )
            
            # 在元数据中添加纹理信息
            result["metadata"]["target_file_id"] = file_objects[0].id
            result["metadata"]["texture_file_id"] = file_objects[1].id
            result["metadata"]["transfer_type"] = "block_matching"
            
            # 更新进度：完成
            if task_id:
                self.update_task_progress(task_id, 100, 100, "Texture transfer completed")
            
            logger.info("texture_transfer_completed",
                       target_file_id=file_objects[0].id,
                       texture_file_id=file_objects[1].id,
                       processing_time=processing_time)
            
            return result
            
        except Exception as e:
            logger.error("texture_transfer_failed",
                        file_objects=[f.id for f in file_objects],
                        error=str(e))
            raise
        finally:
            # 确保清理临时文件
            self.cleanup_temp_files()
    
    def process_texture_transfer(
        self, 
        target_image: np.ndarray, 
        source_texture: np.ndarray, 
        parameters: Dict[str, Any]
    ) -> np.ndarray:
        """
        执行纹理迁移算法
        
        Args:
            target_image: 目标图像
            source_texture: 源纹理图像
            parameters: 处理参数
                - block_size: 块大小，默认32
                - alpha: 纹理强度，范围0.0-1.0，默认0.7
                
        Returns:
            纹理迁移后的图像
        """
        try:
            block_size = parameters.get('block_size', 32)
            alpha = parameters.get('alpha', 0.7)
            
            logger.info("processing_texture_transfer", 
                       target_shape=target_image.shape,
                       texture_shape=source_texture.shape,
                       block_size=block_size,
                       alpha=alpha)
            
            # 调整纹理图像尺寸
            h, w = target_image.shape[:2]
            source_texture_resized = cv2.resize(source_texture, (w, h))
            
            # 执行简化的纹理迁移
            result = self.simple_texture_transfer(target_image, source_texture_resized, block_size, alpha)
            
            logger.info("texture_transfer_algorithm_completed",
                       output_shape=result.shape,
                       block_size=block_size,
                       alpha=alpha)
            
            return result
            
        except Exception as e:
            logger.error("texture_transfer_algorithm_failed", 
                        block_size=block_size,
                        alpha=alpha,
                        error=str(e))
            raise ValueError(f"Texture transfer algorithm failed: {str(e)}")
    
    def simple_texture_transfer(
        self, 
        target_image: np.ndarray, 
        source_texture: np.ndarray, 
        block_size: int, 
        alpha: float
    ) -> np.ndarray:
        """
        简化的纹理迁移算法：基于块的纹理替换
        
        Args:
            target_image: 目标图像
            source_texture: 源纹理图像
            block_size: 块大小
            alpha: 纹理混合强度
            
        Returns:
            纹理迁移后的图像
        """
        h, w = target_image.shape[:2]
        result = target_image.astype(np.float32)
        
        # 转换为灰度图用于块匹配
        target_gray = cv2.cvtColor(target_image, cv2.COLOR_BGR2GRAY)
        texture_gray = cv2.cvtColor(source_texture, cv2.COLOR_BGR2GRAY)
        
        # 预计算纹理块
        texture_blocks = self.extract_texture_blocks(source_texture, texture_gray, block_size)
        
        # 对目标图像的每个块进行纹理替换
        for y in range(0, h - block_size + 1, block_size):
            for x in range(0, w - block_size + 1, block_size):
                # 获取目标块
                target_block = target_image[y:y+block_size, x:x+block_size]
                target_gray_block = target_gray[y:y+block_size, x:x+block_size]
                
                # 找到最匹配的纹理块
                best_block = self.find_best_texture_block(target_gray_block, texture_blocks)
                
                if best_block is not None:
                    # 混合纹理
                    mixed_block = (1 - alpha) * target_block.astype(np.float32) + alpha * best_block.astype(np.float32)
                    result[y:y+block_size, x:x+block_size] = mixed_block
        
        return np.clip(result, 0, 255).astype(np.uint8)
    
    def extract_texture_blocks(
        self, 
        texture_image: np.ndarray, 
        texture_gray: np.ndarray, 
        block_size: int
    ) -> List[np.ndarray]:
        """
        从纹理图像中提取所有可能的块
        
        Args:
            texture_image: 彩色纹理图像
            texture_gray: 灰度纹理图像
            block_size: 块大小
            
        Returns:
            纹理块列表
        """
        blocks = []
        h, w = texture_image.shape[:2]
        
        # 提取所有可能的块（重叠采样）
        step = max(1, block_size // 4)  # 重叠采样以增加块的多样性
        
        for y in range(0, h - block_size + 1, step):
            for x in range(0, w - block_size + 1, step):
                block = texture_image[y:y+block_size, x:x+block_size]
                blocks.append(block)
        
        logger.info("texture_blocks_extracted", 
                   total_blocks=len(blocks),
                   block_size=block_size)
        
        return blocks
    
    def find_best_texture_block(
        self, 
        target_gray_block: np.ndarray, 
        texture_blocks: List[np.ndarray]
    ) -> Optional[np.ndarray]:
        """
        找到与目标块最匹配的纹理块
        
        Args:
            target_gray_block: 目标灰度块
            texture_blocks: 纹理块列表
            
        Returns:
            最匹配的纹理块
        """
        if not texture_blocks:
            return None
        
        best_block = None
        best_score = float('inf')
        
        # 计算目标块的统计特征
        target_mean = np.mean(target_gray_block)
        target_std = np.std(target_gray_block)
        
        for block in texture_blocks:
            # 转换为灰度
            block_gray = cv2.cvtColor(block, cv2.COLOR_BGR2GRAY)
            
            # 计算特征相似度
            block_mean = np.mean(block_gray)
            block_std = np.std(block_gray)
            
            # 简单的特征距离
            mean_diff = abs(target_mean - block_mean)
            std_diff = abs(target_std - block_std)
            score = mean_diff + std_diff
            
            if score < best_score:
                best_score = score
                best_block = block
        
        return best_block
    
    def process_image(self, image: np.ndarray, parameters: Dict[str, Any]) -> np.ndarray:
        """
        单图像处理接口（纹理迁移不适用）
        """
        raise NotImplementedError("Texture transfer requires two images. Use process_multiple_images instead.")


def create_texture_transfer_processor(minio_service: MinIOService, db_session: Session) -> TextureTransferProcessor:
    """
    创建纹理迁移处理器实例
    
    Args:
        minio_service: MinIO服务实例
        db_session: 数据库会话
        
    Returns:
        纹理迁移处理器实例
    """
    return TextureTransferProcessor(minio_service, db_session)


def validate_texture_transfer_parameters(parameters: Dict[str, Any]) -> Dict[str, Any]:
    """
    验证和转换纹理迁移处理参数
    
    Args:
        parameters: 输入参数
        
    Returns:
        验证后的参数
        
    Raises:
        ValueError: 参数无效时抛出
    """
    try:
        # 提取参数
        block_size = parameters.get('block_size', 32)
        alpha = parameters.get('alpha', 0.7)
        
        # 类型转换和验证
        try:
            block_size = int(block_size)
            alpha = float(alpha)
        except (ValueError, TypeError):
            raise ValueError("Block size must be an integer and alpha must be a number")
        
        # 范围检查
        if block_size < 8 or block_size > 128:
            raise ValueError("Block size must be between 8 and 128")
        
        if not (0.0 <= alpha <= 1.0):
            raise ValueError("Alpha must be between 0.0 and 1.0")
        
        return {
            'block_size': block_size,
            'alpha': alpha
        }
        
    except Exception as e:
        logger.error("texture_transfer_parameter_validation_failed", 
                    parameters=parameters,
                    error=str(e))
        raise ValueError(f"Parameter validation failed: {str(e)}")


# 为了向后兼容，提供旧格式的函数接口
def texture_transfer_legacy(target_path: str, texture_path: str, block_size: int = 32, alpha: float = 0.7) -> str:
    """
    旧格式的纹理迁移函数接口（向后兼容）
    
    Args:
        target_path: 目标图像文件路径
        texture_path: 纹理图像文件路径
        block_size: 块大小
        alpha: 纹理强度
        
    Returns:
        处理后的图像路径
        
    Note:
        这个函数仅用于向后兼容，新代码应使用TextureTransferProcessor
    """
    logger.warning("using_legacy_texture_transfer_interface", 
                  target_path=target_path,
                  texture_path=texture_path,
                  block_size=block_size,
                  alpha=alpha)
    
    # 这里可以实现向旧接口的适配逻辑
    # 但在新架构中，建议直接使用TextureTransferProcessor
    raise NotImplementedError("Legacy interface not implemented in new architecture. Use TextureTransferProcessor instead.")
