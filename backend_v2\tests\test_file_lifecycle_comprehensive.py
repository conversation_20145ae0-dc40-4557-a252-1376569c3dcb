#!/usr/bin/env python3
"""
文件生命周期管理综合测试
验证2.2.4任务的完整实现
"""
import sys
import os
import io
from datetime import datetime, timedelta
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from fastapi.testclient import TestClient
from app.main import app
from app.core.database import get_db_session
from app.services.file_lifecycle_service import FileLifecycleService, FileLifecyclePolicy
from app.services.file_usage_service import FileUsageService

def test_lifecycle_policies_comprehensive():
    """测试文件生命周期策略的完整功能"""
    print("🧪 测试文件生命周期策略...")
    
    try:
        # 测试策略识别
        test_cases = [
            ("uploads/images/test.jpg", FileLifecyclePolicy.RAW_FILES, 90),
            ("processed/results/output.jpg", FileLifecyclePolicy.RESULT_FILES, None),
            ("thumbnails/small/thumb.jpg", FileLifecyclePolicy.THUMBNAIL_FILES, 180),
            ("temp/session/temp.jpg", FileLifecyclePolicy.TEMP_FILES, 7),
        ]
        
        for path, expected_policy, expected_days in test_cases:
            policy = FileLifecycleService.get_file_policy(path)
            retention_days = FileLifecycleService.LIFECYCLE_POLICIES.get(policy)
            
            if policy == expected_policy and retention_days == expected_days:
                print(f"✅ 路径 '{path}' -> 策略 '{policy.value}' (保留{retention_days}天)")
            else:
                print(f"❌ 路径策略测试失败: {path}")
                return False
        
        # 测试过期计算
        now = datetime.now()
        
        # 测试已过期文件
        old_date = now - timedelta(days=100)  # 100天前
        raw_policy = FileLifecyclePolicy.RAW_FILES
        
        expiry_date = FileLifecycleService.calculate_expiry_date(old_date, raw_policy)
        expected_expiry = old_date + timedelta(days=90)
        
        if expiry_date and expiry_date < now:
            print(f"✅ 过期计算正确: 100天前的原始文件已过期")
        else:
            print(f"❌ 过期计算错误")
            return False
        
        # 测试永久保存文件
        result_expiry = FileLifecycleService.calculate_expiry_date(old_date, FileLifecyclePolicy.RESULT_FILES)
        if result_expiry is None:
            print(f"✅ 结果文件永久保存策略正确")
        else:
            print(f"❌ 结果文件应该永久保存")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 策略测试失败: {str(e)}")
        return False

def test_lifecycle_apis_comprehensive():
    """测试生命周期管理API的完整功能"""
    print("\n🧪 测试生命周期管理API...")
    
    try:
        client = TestClient(app)
        
        # 1. 测试生命周期统计API
        print("🔄 测试生命周期统计API...")
        stats_response = client.get("/api/files/lifecycle/statistics")
        
        if stats_response.status_code == 200:
            stats = stats_response.json()
            print(f"✅ 生命周期统计: 总文件{stats.get('total_files', 0)}个")
            
            by_policy = stats.get('by_policy', {})
            for policy, policy_stats in by_policy.items():
                print(f"   - {policy}: {policy_stats.get('count', 0)}个文件")
        else:
            print(f"❌ 生命周期统计API失败: {stats_response.status_code}")
            return False
        
        # 2. 测试过期文件查询API
        print("🔄 测试过期文件查询API...")
        expired_response = client.get("/api/files/lifecycle/expired?limit=5")
        
        if expired_response.status_code == 200:
            expired_result = expired_response.json()
            print(f"✅ 过期文件查询: 发现{expired_result.get('total_found', 0)}个过期文件")
        else:
            print(f"❌ 过期文件查询API失败: {expired_response.status_code}")
            return False
        
        # 3. 测试清理任务触发API
        print("🔄 测试清理任务触发API...")
        cleanup_response = client.post("/api/files/lifecycle/cleanup?dry_run=true")
        
        if cleanup_response.status_code == 200:
            cleanup_result = cleanup_response.json()
            print(f"✅ 清理任务触发成功: 任务ID {cleanup_result.get('task_id')}")
        else:
            print(f"❌ 清理任务触发API失败: {cleanup_response.status_code}")
            return False
        
        # 4. 测试归档任务触发API
        print("🔄 测试归档任务触发API...")
        archive_response = client.post("/api/files/lifecycle/archive?archive_days=365")
        
        if archive_response.status_code == 200:
            archive_result = archive_response.json()
            print(f"✅ 归档任务触发成功: 任务ID {archive_result.get('task_id')}")
        else:
            print(f"❌ 归档任务触发API失败: {archive_response.status_code}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ API测试失败: {str(e)}")
        return False

def test_usage_tracking_comprehensive():
    """测试文件使用统计的完整功能"""
    print("\n🧪 测试文件使用统计...")
    
    try:
        client = TestClient(app)
        
        # 上传测试文件
        test_file_content = b"Comprehensive usage tracking test file"
        test_file = io.BytesIO(test_file_content)
        
        files = {
            "file": ("comprehensive_test.jpg", test_file, "image/jpeg")
        }
        
        print("🔄 上传测试文件...")
        upload_response = client.post("/api/files/upload", files=files)
        
        if upload_response.status_code != 200:
            print(f"❌ 文件上传失败: {upload_response.status_code}")
            return False
        
        result = upload_response.json()
        file_db_id = result.get('database_id')
        print(f"✅ 文件上传成功，ID: {file_db_id}")
        
        # 模拟多次访问
        db = get_db_session()
        try:
            # 记录不同类型的访问
            access_types = ["view", "download", "preview", "view", "download"]
            for access_type in access_types:
                FileUsageService.record_file_access(db=db, file_id=file_db_id, access_type=access_type)
            
            print(f"✅ 记录了{len(access_types)}次文件访问")
            
            # 测试使用统计API
            print("🔄 测试使用统计API...")
            
            # 1. 文件使用统计
            usage_response = client.get(f"/api/files/{file_db_id}/usage")
            if usage_response.status_code == 200:
                usage_stats = usage_response.json()
                total_accesses = usage_stats.get('total_accesses', 0)
                print(f"✅ 文件使用统计: {total_accesses}次访问")
                
                if total_accesses != len(access_types):
                    print(f"❌ 访问次数不匹配: 期望{len(access_types)}, 实际{total_accesses}")
                    return False
            else:
                print(f"❌ 文件使用统计API失败: {usage_response.status_code}")
                return False
            
            # 2. 使用摘要
            summary_response = client.get("/api/files/usage/summary")
            if summary_response.status_code == 200:
                summary = summary_response.json()
                print(f"✅ 使用摘要: 访问率{summary.get('access_rate', 0):.1f}%")
            else:
                print(f"❌ 使用摘要API失败: {summary_response.status_code}")
                return False
            
            # 3. 热门文件
            popular_response = client.get("/api/files/usage/popular?limit=5&days=1")
            if popular_response.status_code == 200:
                popular_result = popular_response.json()
                popular_count = popular_result.get('total_found', 0)
                print(f"✅ 热门文件查询: 发现{popular_count}个热门文件")
            else:
                print(f"❌ 热门文件API失败: {popular_response.status_code}")
                return False
            
            # 4. 未使用文件
            unused_response = client.get("/api/files/usage/unused?days=1&limit=5")
            if unused_response.status_code == 200:
                unused_result = unused_response.json()
                unused_count = unused_result.get('total_found', 0)
                print(f"✅ 未使用文件查询: 发现{unused_count}个未使用文件")
            else:
                print(f"❌ 未使用文件API失败: {unused_response.status_code}")
                return False
            
            return True
            
        finally:
            db.close()
            
    except Exception as e:
        print(f"❌ 使用统计测试失败: {str(e)}")
        return False

def test_download_usage_integration():
    """测试下载与使用统计的集成"""
    print("\n🧪 测试下载与使用统计集成...")
    
    try:
        client = TestClient(app)
        
        # 上传文件
        test_file_content = b"Download integration test file"
        test_file = io.BytesIO(test_file_content)
        
        files = {
            "file": ("download_integration.jpg", test_file, "image/jpeg")
        }
        
        upload_response = client.post("/api/files/upload", files=files)
        if upload_response.status_code != 200:
            print(f"❌ 文件上传失败: {upload_response.status_code}")
            return False
        
        result = upload_response.json()
        file_db_id = result.get('database_id')
        
        # 下载文件（应该自动记录使用统计）
        download_response = client.get(f"/api/files/{file_db_id}/download", follow_redirects=False)
        
        if download_response.status_code == 302:
            print("✅ 下载重定向成功")
            
            # 检查使用统计是否被记录
            usage_response = client.get(f"/api/files/{file_db_id}/usage")
            if usage_response.status_code == 200:
                usage_stats = usage_response.json()
                download_count = usage_stats.get('access_types', {}).get('download', 0)
                
                if download_count > 0:
                    print(f"✅ 下载使用统计记录成功: {download_count}次下载")
                    return True
                else:
                    print(f"❌ 下载使用统计未记录")
                    return False
            else:
                print(f"❌ 使用统计查询失败: {usage_response.status_code}")
                return False
        else:
            print(f"❌ 下载失败: {download_response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 下载集成测试失败: {str(e)}")
        return False

if __name__ == "__main__":
    print("🚀 开始文件生命周期管理综合测试...")
    
    success1 = test_lifecycle_policies_comprehensive()
    success2 = test_lifecycle_apis_comprehensive()
    success3 = test_usage_tracking_comprehensive()
    success4 = test_download_usage_integration()
    
    if success1 and success2 and success3 and success4:
        print("\n🎉 所有文件生命周期管理综合测试通过！")
        print("\n📋 2.2.4任务验证完成:")
        print("✅ 文件过期策略：raw/90天，result/永久，thumb/180天，temp/7天")
        print("✅ 定时任务配置：清理、归档、统计、临时文件清理")
        print("✅ 文件状态跟踪：访问统计、使用频率、热门文件")
        print("✅ 文件使用监控：下载统计、未使用文件识别")
        sys.exit(0)
    else:
        print("\n💥 文件生命周期管理综合测试失败！")
        sys.exit(1)
