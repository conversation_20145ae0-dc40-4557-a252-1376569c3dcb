"""
视频参数转换器
扩展ParameterConverter以支持视频特有的参数验证和转换
基于简洁设计理念，专注核心功能，避免过度复杂化
"""

from typing import Dict, Any, Tuple, Optional, List
import structlog
from app.services.image_processor_base import ParameterConverter
from app.services.memory_monitor import PerformanceConfig

logger = structlog.get_logger()


class VideoParameterConverter(ParameterConverter):
    """视频参数转换器，继承并扩展图像参数转换器"""
    
    # 支持的视频编码格式
    SUPPORTED_CODECS = [
        'libx264', 'h264', 'h264_nvenc', 'h264_qsv',
        'libx265', 'h265', 'hevc', 'hevc_nvenc', 'hevc_qsv',
        'libvpx', 'libvpx-vp9', 'vp8', 'vp9',
        'mpeg4', 'libxvid'
    ]
    
    # 支持的像素格式
    SUPPORTED_PIXEL_FORMATS = [
        'yuv420p', 'yuv422p', 'yuv444p',
        'rgb24', 'bgr24', 'rgba', 'bgra',
        'gray', 'nv12', 'nv21'
    ]
    
    # 常见分辨率预设
    RESOLUTION_PRESETS = {
        '480p': (854, 480),
        '720p': (1280, 720),
        '1080p': (1920, 1080),
        '1440p': (2560, 1440),
        '4k': (3840, 2160),
        '8k': (7680, 4320)
    }
    
    @staticmethod
    def convert_legacy_parameters(operation: str, legacy_params: Dict[str, Any]) -> Dict[str, Any]:
        """
        将旧格式参数转换为新格式，支持视频特有参数
        
        Args:
            operation: 操作名称
            legacy_params: 旧格式参数
            
        Returns:
            新格式参数
        """
        # 先调用父类的转换方法
        converted_params = ParameterConverter.convert_legacy_parameters(operation, legacy_params)
        
        # 处理视频特有的参数转换
        if operation in ["video_grayscale", "video_resize", "video_format_conversion"]:
            # 处理分辨率参数
            if "resolution" in converted_params:
                resolution = converted_params["resolution"]
                if isinstance(resolution, str) and resolution in VideoParameterConverter.RESOLUTION_PRESETS:
                    # 转换分辨率预设
                    width, height = VideoParameterConverter.RESOLUTION_PRESETS[resolution]
                    converted_params["width"] = width
                    converted_params["height"] = height
                    del converted_params["resolution"]
            
            # 处理编码格式参数
            if "codec" in converted_params:
                codec = converted_params["codec"].lower()
                # 标准化编码格式名称
                if codec in ["h264", "avc"]:
                    converted_params["codec"] = "libx264"
                elif codec in ["h265", "hevc"]:
                    converted_params["codec"] = "libx265"
                elif codec in ["vp8"]:
                    converted_params["codec"] = "libvpx"
                elif codec in ["vp9"]:
                    converted_params["codec"] = "libvpx-vp9"
            
            # 处理帧率参数
            if "fps" in converted_params:
                fps = converted_params["fps"]
                if isinstance(fps, str):
                    try:
                        converted_params["fps"] = float(fps)
                    except ValueError:
                        # 保持原值，让验证阶段处理
                        pass
            
            # 处理质量参数
            if "quality" in converted_params:
                quality = converted_params["quality"]
                if isinstance(quality, str):
                    # 转换质量预设
                    quality_map = {
                        "low": 28,
                        "medium": 23,
                        "high": 18,
                        "best": 15
                    }
                    if quality.lower() in quality_map:
                        converted_params["crf"] = quality_map[quality.lower()]
                        del converted_params["quality"]
        
        return converted_params
    
    @staticmethod
    def validate_parameters(operation: str, parameters: Dict[str, Any]) -> Tuple[bool, str]:
        """
        验证参数的有效性，支持视频特有参数
        
        Args:
            operation: 操作名称
            parameters: 参数字典
            
        Returns:
            (是否有效, 错误信息)
        """
        # 先调用父类的验证方法
        is_valid, error_msg = ParameterConverter.validate_parameters(operation, parameters)
        if not is_valid:
            return is_valid, error_msg
        
        try:
            # 视频特有参数验证
            if operation in ["video_grayscale", "video_resize", "video_format_conversion"]:
                
                # 验证分辨率参数
                if "width" in parameters or "height" in parameters:
                    width = parameters.get("width")
                    height = parameters.get("height")
                    
                    if width is not None:
                        if not isinstance(width, int) or not (32 <= width <= 7680):
                            return False, "Width must be an integer between 32 and 7680"
                    
                    if height is not None:
                        if not isinstance(height, int) or not (32 <= height <= 4320):
                            return False, "Height must be an integer between 32 and 4320"
                    
                    # 如果指定了宽度和高度，检查比例合理性
                    if width and height:
                        aspect_ratio = width / height
                        if not (0.1 <= aspect_ratio <= 10.0):
                            return False, "Aspect ratio must be between 0.1 and 10.0"
                
                # 验证帧率参数
                if "fps" in parameters:
                    fps = parameters["fps"]
                    if not isinstance(fps, (int, float)) or not (1.0 <= fps <= 120.0):
                        return False, "FPS must be between 1.0 and 120.0"
                
                # 验证编码格式参数
                if "codec" in parameters:
                    codec = parameters["codec"]
                    if not isinstance(codec, str) or codec not in VideoParameterConverter.SUPPORTED_CODECS:
                        return False, f"Codec must be one of: {', '.join(VideoParameterConverter.SUPPORTED_CODECS)}"
                
                # 验证像素格式参数
                if "pixel_format" in parameters:
                    pixel_format = parameters["pixel_format"]
                    if not isinstance(pixel_format, str) or pixel_format not in VideoParameterConverter.SUPPORTED_PIXEL_FORMATS:
                        return False, f"Pixel format must be one of: {', '.join(VideoParameterConverter.SUPPORTED_PIXEL_FORMATS)}"
                
                # 验证质量参数
                if "crf" in parameters:
                    crf = parameters["crf"]
                    if not isinstance(crf, int) or not (0 <= crf <= 51):
                        return False, "CRF must be an integer between 0 and 51"
                
                # 验证预设参数
                if "preset" in parameters:
                    preset = parameters["preset"]
                    valid_presets = ["ultrafast", "superfast", "veryfast", "faster", "fast", "medium", "slow", "slower", "veryslow"]
                    if not isinstance(preset, str) or preset not in valid_presets:
                        return False, f"Preset must be one of: {', '.join(valid_presets)}"
            
            return True, "Parameters are valid"
            
        except Exception as e:
            return False, f"Video parameter validation error: {str(e)}"
    
    @staticmethod
    def get_default_parameters(operation: str) -> Dict[str, Any]:
        """
        获取操作的默认参数
        
        Args:
            operation: 操作名称
            
        Returns:
            默认参数字典
        """
        defaults = {
            "video_grayscale": {},
            "video_resize": {
                "width": 1280,
                "height": 720,
                "maintain_aspect_ratio": True
            },
            "video_format_conversion": {
                "codec": "libx264",
                "preset": "fast",
                "crf": 23,
                "pixel_format": "yuv420p"
            }
        }
        
        return defaults.get(operation, {})
    
    @staticmethod
    def merge_with_performance_config(
        parameters: Dict[str, Any], 
        performance_config: PerformanceConfig
    ) -> Dict[str, Any]:
        """
        将性能配置合并到参数中
        
        Args:
            parameters: 原始参数
            performance_config: 性能配置
            
        Returns:
            合并后的参数
        """
        merged_params = parameters.copy()
        
        # 添加性能相关参数
        merged_params["batch_size"] = performance_config.batch_size
        merged_params["memory_limit_mb"] = performance_config.memory_limit_mb
        merged_params["progress_update_interval"] = performance_config.progress_update_interval
        
        logger.debug("parameters_merged_with_performance_config",
                    original_params=parameters,
                    performance_config=performance_config.__dict__,
                    merged_params=merged_params)
        
        return merged_params
    
    @staticmethod
    def extract_video_processing_parameters(parameters: Dict[str, Any]) -> Tuple[Dict[str, Any], Dict[str, Any]]:
        """
        从参数中分离视频处理参数和性能参数
        
        Args:
            parameters: 完整参数字典
            
        Returns:
            (视频处理参数, 性能参数)
        """
        # 性能相关参数
        performance_keys = {"batch_size", "memory_limit_mb", "progress_update_interval"}
        
        # 分离参数
        video_params = {k: v for k, v in parameters.items() if k not in performance_keys}
        performance_params = {k: v for k, v in parameters.items() if k in performance_keys}
        
        return video_params, performance_params
