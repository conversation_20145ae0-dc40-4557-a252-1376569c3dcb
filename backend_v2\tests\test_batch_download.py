#!/usr/bin/env python3
"""
测试批量下载和文件下载API功能
验证2.2.5任务的完整实现
"""
import sys
import os
import io
import zipfile
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from fastapi.testclient import TestClient
from app.main import app
from app.core.database import get_db_session
from app.services.batch_download_service import BatchDownloadService

def test_batch_download_permission_validation():
    """测试批量下载权限验证"""
    print("🧪 测试批量下载权限验证...")
    
    try:
        db = get_db_session()
        
        # 测试空文件列表
        has_permission, error_msg, files = BatchDownloadService.validate_download_permission(
            db=db, file_ids=[]
        )
        print(f"✅ 空文件列表验证: {has_permission}, {error_msg}")
        
        # 测试不存在的文件ID
        has_permission, error_msg, files = BatchDownloadService.validate_download_permission(
            db=db, file_ids=[99999]
        )
        if not has_permission and "not found" in error_msg:
            print(f"✅ 不存在文件验证: {error_msg}")
        else:
            print(f"❌ 不存在文件验证失败")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 权限验证测试失败: {str(e)}")
        return False
    finally:
        if 'db' in locals():
            db.close()

def test_download_statistics():
    """测试下载统计功能"""
    print("\n🧪 测试下载统计功能...")
    
    try:
        client = TestClient(app)
        
        # 测试下载统计API
        print("🔄 测试下载统计API...")
        stats_response = client.post("/api/files/download/statistics", json=[1, 2, 3])
        
        print(f"📡 统计响应状态码: {stats_response.status_code}")
        
        if stats_response.status_code == 200:
            stats = stats_response.json()
            print(f"✅ 下载统计获取成功!")
            print(f"📊 文件数量: {stats.get('file_count', 0)}")
            print(f"📏 总大小: {stats.get('total_size_mb', 0)} MB")
            print(f"📦 估算ZIP大小: {stats.get('estimated_zip_size_mb', 0)} MB")
            
            file_types = stats.get('file_types', {})
            if file_types:
                print(f"📂 文件类型分布: {file_types}")
            
            return True
        else:
            print(f"❌ 下载统计获取失败: {stats_response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 下载统计测试失败: {str(e)}")
        return False

def test_batch_download_api():
    """测试批量下载API"""
    print("\n🧪 测试批量下载API...")
    
    try:
        client = TestClient(app)
        
        # 先上传几个测试文件
        uploaded_files = []
        
        for i in range(2):
            test_file_content = f"Test file content {i+1} for batch download".encode()
            test_file = io.BytesIO(test_file_content)

            files = {
                "file": (f"batch_test_{i+1}.jpg", test_file, "image/jpeg")
            }
            
            upload_response = client.post("/api/files/upload", files=files)
            if upload_response.status_code == 200:
                result = upload_response.json()
                uploaded_files.append(result.get('database_id'))
                print(f"✅ 上传测试文件 {i+1}: ID {result.get('database_id')}")
            else:
                print(f"❌ 上传测试文件 {i+1} 失败: {upload_response.status_code}")
                return False
        
        if len(uploaded_files) < 2:
            print("❌ 需要至少2个文件进行批量下载测试")
            return False
        
        # 测试批量下载
        print("🔄 测试批量下载...")
        download_response = client.post("/api/files/download-zip", json=uploaded_files)
        
        print(f"📡 批量下载响应状态码: {download_response.status_code}")
        
        if download_response.status_code == 200:
            print("✅ 批量下载请求成功!")
            
            # 检查响应头
            headers = download_response.headers
            content_disposition = headers.get('content-disposition', '')
            file_count = headers.get('x-file-count', '0')
            total_size = headers.get('x-total-size', '0')
            
            print(f"📦 Content-Disposition: {content_disposition}")
            print(f"📊 文件数量: {file_count}")
            print(f"📏 总大小: {total_size} bytes")
            
            # 检查是否是ZIP文件
            if 'application/zip' in headers.get('content-type', ''):
                print("✅ 返回的是ZIP文件")
                
                # 尝试解析ZIP内容
                try:
                    zip_content = download_response.content
                    zip_file = zipfile.ZipFile(io.BytesIO(zip_content))
                    file_list = zip_file.namelist()
                    print(f"📂 ZIP文件包含: {file_list}")
                    
                    if len(file_list) >= len(uploaded_files):
                        print("✅ ZIP文件包含预期数量的文件")
                        return True
                    else:
                        print(f"❌ ZIP文件数量不匹配: 期望{len(uploaded_files)}, 实际{len(file_list)}")
                        return False
                        
                except Exception as e:
                    print(f"❌ ZIP文件解析失败: {str(e)}")
                    return False
            else:
                print(f"❌ 返回的不是ZIP文件: {headers.get('content-type')}")
                return False
        else:
            print(f"❌ 批量下载失败: {download_response.status_code}")
            print(f"错误详情: {download_response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 批量下载API测试失败: {str(e)}")
        return False

def test_result_download_api():
    """测试结果下载API"""
    print("\n🧪 测试结果下载API...")
    
    try:
        client = TestClient(app)
        
        # 测试不存在的结果ID
        print("🔄 测试不存在的结果ID...")
        result_response = client.get("/api/files/download/99999")
        
        print(f"📡 结果下载响应状态码: {result_response.status_code}")
        
        if result_response.status_code == 404:
            print("✅ 不存在的结果ID正确返回404")
        elif result_response.status_code == 403:
            print("✅ 不存在的结果ID正确返回403（权限验证）")
        else:
            print(f"⚠️ 不存在的结果ID返回: {result_response.status_code}")
        
        # 测试格式参数
        print("🔄 测试格式参数...")
        format_response = client.get("/api/files/download/99999?format=redirect")
        
        print(f"📡 格式参数响应状态码: {format_response.status_code}")
        
        if format_response.status_code in [404, 403]:
            print("✅ 格式参数处理正常")
        else:
            print(f"⚠️ 格式参数返回: {format_response.status_code}")
        
        return True
        
    except Exception as e:
        print(f"❌ 结果下载API测试失败: {str(e)}")
        return False

def test_download_integration():
    """测试下载功能集成"""
    print("\n🧪 测试下载功能集成...")
    
    try:
        client = TestClient(app)
        
        # 上传一个文件
        test_file_content = b"Integration test file for download"
        test_file = io.BytesIO(test_file_content)

        files = {
            "file": ("integration_test.jpg", test_file, "image/jpeg")
        }
        
        upload_response = client.post("/api/files/upload", files=files)
        if upload_response.status_code != 200:
            print(f"❌ 文件上传失败: {upload_response.status_code}")
            return False
        
        result = upload_response.json()
        file_db_id = result.get('database_id')
        print(f"✅ 文件上传成功，ID: {file_db_id}")
        
        # 测试单文件下载
        print("🔄 测试单文件下载...")
        download_response = client.get(f"/api/files/{file_db_id}/download", follow_redirects=False)
        
        if download_response.status_code == 302:
            print("✅ 单文件下载重定向成功")
            
            # 检查使用统计是否被记录
            usage_response = client.get(f"/api/files/{file_db_id}/usage")
            if usage_response.status_code == 200:
                usage_stats = usage_response.json()
                download_count = usage_stats.get('access_types', {}).get('download', 0)
                print(f"✅ 下载统计记录: {download_count}次下载")
            
            return True
        else:
            print(f"❌ 单文件下载失败: {download_response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 下载集成测试失败: {str(e)}")
        return False

if __name__ == "__main__":
    print("🚀 开始批量下载和文件下载API测试...")
    
    success1 = test_batch_download_permission_validation()
    success2 = test_download_statistics()
    success3 = test_batch_download_api()
    success4 = test_result_download_api()
    success5 = test_download_integration()
    
    if success1 and success2 and success3 and success4 and success5:
        print("\n🎉 所有批量下载和文件下载API测试通过！")
        print("\n📋 2.2.5任务验证完成:")
        print("✅ 批量下载权限验证：正确验证文件存在性和状态")
        print("✅ 流式ZIP打包：成功创建包含多个文件的ZIP")
        print("✅ 下载统计功能：提供文件数量、大小和类型统计")
        print("✅ 结果下载API：支持任务/批次结果下载")
        print("✅ 下载集成：单文件下载和使用统计记录正常")
        sys.exit(0)
    else:
        print("\n💥 批量下载和文件下载API测试失败！")
        sys.exit(1)
