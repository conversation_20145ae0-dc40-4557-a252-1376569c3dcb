#!/usr/bin/env python3
"""
测试任务持久化服务功能
验证2.1.2.4的实现
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.schemas.task import TaskCreate
from app.schemas.batch import BatchCreate
from app.schemas.file import FileCreate
from app.models.file import FileType, FileStatus
from app.models.batch import BatchStatus
from app.models.task import TaskStatus
from app.services.task_persistence import TaskPersistenceService, TaskPersistenceError

def test_task_persistence_service():
    """测试任务持久化服务"""
    print("🧪 测试任务持久化服务...")
    
    # 测试数据准备
    task_data = TaskCreate(
        name="测试持久化任务",
        description="测试任务持久化服务的功能",
        config={
            "task_type": "image_sharpen",
            "parameters": {"intensity": 1.0},
            "test_mode": True
        }
    )
    
    batches_data = [
        BatchCreate(
            task_id=0,  # 将在服务中设置
            name="测试批次1",
            description="第一个测试批次",
            operation_type="image_sharpen",
            queue_name="cpu",
            config={"batch_index": 1}
        ),
        BatchCreate(
            task_id=0,
            name="测试批次2", 
            description="第二个测试批次",
            operation_type="image_sharpen",
            queue_name="cpu",
            config={"batch_index": 2}
        )
    ]
    
    files_data = [
        FileCreate(
            batch_id=0,  # 批次索引，将被替换为真实ID
            filename="test1.jpg",
            original_filename="test1.jpg",
            file_type=FileType.IMAGE,
            storage_path="test/test1.jpg",
            storage_bucket="test-bucket",
            file_size=1024000
        ),
        FileCreate(
            batch_id=0,
            filename="test2.jpg",
            original_filename="test2.jpg", 
            file_type=FileType.IMAGE,
            storage_path="test/test2.jpg",
            storage_bucket="test-bucket",
            file_size=2048000
        ),
        FileCreate(
            batch_id=1,  # 第二个批次
            filename="test3.jpg",
            original_filename="test3.jpg",
            file_type=FileType.IMAGE,
            storage_path="test/test3.jpg",
            storage_bucket="test-bucket",
            file_size=1536000
        )
    ]
    
    print(f"✅ 准备测试数据:")
    print(f"   - 任务: {task_data.name}")
    print(f"   - 批次数: {len(batches_data)}")
    print(f"   - 文件数: {len(files_data)}")
    
    # 验证数据结构
    assert task_data.name == "测试持久化任务"
    assert len(batches_data) == 2
    assert len(files_data) == 3
    assert files_data[0].batch_id == 0  # 第一个批次
    assert files_data[2].batch_id == 1  # 第二个批次
    
    print("✅ 测试数据结构验证通过")

def test_batch_file_assignment():
    """测试批次文件分配逻辑"""
    print("\n🧪 测试批次文件分配逻辑...")
    
    # 模拟文件分配到批次
    files = ["file1.jpg", "file2.jpg", "file3.jpg", "file4.jpg", "file5.jpg"]
    batch_count = 2
    files_per_batch = len(files) // batch_count
    
    batch_assignments = []
    for i, filename in enumerate(files):
        batch_index = i // files_per_batch
        if batch_index >= batch_count:
            batch_index = batch_count - 1
        batch_assignments.append((filename, batch_index))
    
    print(f"✅ 文件分配结果:")
    for filename, batch_index in batch_assignments:
        print(f"   - {filename} -> 批次 {batch_index}")
    
    # 验证分配结果
    batch_0_files = [f for f, b in batch_assignments if b == 0]
    batch_1_files = [f for f, b in batch_assignments if b == 1]
    
    assert len(batch_0_files) == 2, f"批次0应该有2个文件，实际有{len(batch_0_files)}个"
    assert len(batch_1_files) == 3, f"批次1应该有3个文件，实际有{len(batch_1_files)}个"
    assert len(batch_0_files) + len(batch_1_files) == len(files), "文件总数不匹配"
    
    print("✅ 批次文件分配逻辑验证通过")

def test_task_statistics_structure():
    """测试任务统计信息结构"""
    print("\n🧪 测试任务统计信息结构...")
    
    # 模拟统计信息结构
    mock_statistics = {
        "total_files": 5,
        "processed_files": 3,
        "failed_files": 1,
        "total_size": 10485760  # 10MB
    }
    
    # 验证统计信息结构
    required_fields = ["total_files", "processed_files", "failed_files", "total_size"]
    for field in required_fields:
        assert field in mock_statistics, f"统计信息缺少字段: {field}"
        assert isinstance(mock_statistics[field], int), f"字段 {field} 应该是整数"
    
    print(f"✅ 统计信息结构:")
    for field, value in mock_statistics.items():
        print(f"   - {field}: {value}")
    
    print("✅ 任务统计信息结构验证通过")

def test_task_details_structure():
    """测试任务详情结构"""
    print("\n🧪 测试任务详情结构...")
    
    # 模拟任务详情结构
    mock_task_details = {
        "task": {
            "id": 1,
            "name": "测试任务",
            "status": "pending",
            "progress": 0.0,
            "total_batches": 2,
            "completed_batches": 0,
            "failed_batches": 0
        },
        "batches": [
            {
                "id": 1,
                "name": "批次1",
                "status": "pending",
                "total_files": 2,
                "processed_files": 0,
                "files": []
            },
            {
                "id": 2,
                "name": "批次2", 
                "status": "pending",
                "total_files": 3,
                "processed_files": 0,
                "files": []
            }
        ],
        "statistics": {
            "total_files": 5,
            "processed_files": 0,
            "failed_files": 0,
            "total_size": 0
        }
    }
    
    # 验证任务详情结构
    assert "task" in mock_task_details, "缺少task字段"
    assert "batches" in mock_task_details, "缺少batches字段"
    assert "statistics" in mock_task_details, "缺少statistics字段"
    
    # 验证任务信息
    task_info = mock_task_details["task"]
    required_task_fields = ["id", "name", "status", "progress", "total_batches"]
    for field in required_task_fields:
        assert field in task_info, f"任务信息缺少字段: {field}"
    
    # 验证批次信息
    batches = mock_task_details["batches"]
    assert len(batches) == 2, "应该有2个批次"
    
    for batch in batches:
        required_batch_fields = ["id", "name", "status", "total_files", "files"]
        for field in required_batch_fields:
            assert field in batch, f"批次信息缺少字段: {field}"
    
    print(f"✅ 任务详情结构验证通过:")
    print(f"   - 任务: {task_info['name']} (状态: {task_info['status']})")
    print(f"   - 批次数: {len(batches)}")
    print(f"   - 总文件数: {mock_task_details['statistics']['total_files']}")
    
    print("✅ 任务详情结构验证通过")

def test_error_handling():
    """测试错误处理"""
    print("\n🧪 测试错误处理...")
    
    # 测试TaskPersistenceError
    try:
        raise TaskPersistenceError("测试错误")
    except TaskPersistenceError as e:
        assert str(e) == "测试错误"
        print("✅ TaskPersistenceError 正常工作")
    
    # 测试错误信息格式
    error_message = "创建任务失败: 数据库连接错误"
    assert "创建任务失败" in error_message
    assert "数据库连接错误" in error_message
    print("✅ 错误信息格式正确")
    
    print("✅ 错误处理验证通过")

def test_service_integration():
    """测试服务集成"""
    print("\n🧪 测试服务集成...")
    
    # 验证服务类存在
    assert hasattr(TaskPersistenceService, 'create_complete_task'), "缺少create_complete_task方法"
    assert hasattr(TaskPersistenceService, 'get_task_with_details'), "缺少get_task_with_details方法"
    assert hasattr(TaskPersistenceService, 'update_task_progress'), "缺少update_task_progress方法"
    assert hasattr(TaskPersistenceService, 'get_task_statistics'), "缺少get_task_statistics方法"
    assert hasattr(TaskPersistenceService, 'delete_task_cascade'), "缺少delete_task_cascade方法"
    
    print("✅ 服务方法完整性验证通过:")
    methods = [
        "create_complete_task",
        "get_task_with_details", 
        "update_task_progress",
        "get_task_statistics",
        "delete_task_cascade"
    ]
    
    for method in methods:
        print(f"   - {method}: ✓")
    
    print("✅ 服务集成验证通过")

if __name__ == "__main__":
    print("🚀 开始测试任务2.1.2.4的实现...")
    
    try:
        # 运行所有测试
        test_task_persistence_service()
        test_batch_file_assignment()
        test_task_statistics_structure()
        test_task_details_structure()
        test_error_handling()
        test_service_integration()
        
        print("\n🎉 所有测试通过！")
        print("✅ 任务2.1.2.4 - 创建任务持久化的服务层 验证完成")
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
