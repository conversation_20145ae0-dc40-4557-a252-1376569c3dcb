"""
文件生命周期管理服务
负责文件的过期策略、清理任务和状态跟踪
"""
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional
from enum import Enum
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_

from app.models.file import File, FileStatus, FileType
from app.crud.crud_file import file as file_crud
from app.services.minio_service import MinIOService
from app.utils.storage_path import StorageType


class FileLifecyclePolicy(Enum):
    """文件生命周期策略"""
    RAW_FILES = "raw_files"        # 原始文件：90天
    RESULT_FILES = "result_files"  # 结果文件：永久保存
    THUMBNAIL_FILES = "thumbnail_files"  # 缩略图：180天
    TEMP_FILES = "temp_files"      # 临时文件：7天


class FileLifecycleService:
    """文件生命周期管理服务"""
    
    # 文件过期策略配置（天数）
    LIFECYCLE_POLICIES = {
        FileLifecyclePolicy.RAW_FILES: 90,
        FileLifecyclePolicy.RESULT_FILES: None,  # 永久保存
        FileLifecyclePolicy.THUMBNAIL_FILES: 180,
        FileLifecyclePolicy.TEMP_FILES: 7
    }
    
    @classmethod
    def get_file_policy(cls, storage_path: str) -> FileLifecyclePolicy:
        """
        根据存储路径确定文件生命周期策略
        
        Args:
            storage_path: 文件存储路径
            
        Returns:
            文件生命周期策略
        """
        if storage_path.startswith("uploads/"):
            return FileLifecyclePolicy.RAW_FILES
        elif storage_path.startswith("processed/"):
            return FileLifecyclePolicy.RESULT_FILES
        elif storage_path.startswith("thumbnails/"):
            return FileLifecyclePolicy.THUMBNAIL_FILES
        elif storage_path.startswith("temp/"):
            return FileLifecyclePolicy.TEMP_FILES
        else:
            return FileLifecyclePolicy.RAW_FILES  # 默认策略
    
    @classmethod
    def calculate_expiry_date(cls, file_created_at: datetime, policy: FileLifecyclePolicy) -> Optional[datetime]:
        """
        计算文件过期时间
        
        Args:
            file_created_at: 文件创建时间
            policy: 生命周期策略
            
        Returns:
            过期时间，None表示永不过期
        """
        retention_days = cls.LIFECYCLE_POLICIES.get(policy)
        if retention_days is None:
            return None  # 永久保存
        
        return file_created_at + timedelta(days=retention_days)
    
    @classmethod
    def is_file_expired(cls, file_obj: File) -> bool:
        """
        检查文件是否已过期
        
        Args:
            file_obj: 文件对象
            
        Returns:
            是否已过期
        """
        policy = cls.get_file_policy(file_obj.storage_path)
        expiry_date = cls.calculate_expiry_date(file_obj.created_at, policy)
        
        if expiry_date is None:
            return False  # 永不过期
        
        return datetime.now() > expiry_date
    
    @classmethod
    def find_expired_files(cls, db: Session, limit: int = 100) -> List[File]:
        """
        查找已过期的文件
        
        Args:
            db: 数据库会话
            limit: 限制返回数量
            
        Returns:
            过期文件列表
        """
        expired_files = []
        
        # 查询所有非删除状态的文件
        files = db.query(File).filter(
            File.status != FileStatus.DELETED
        ).limit(limit * 2).all()  # 多查询一些，因为需要过滤
        
        for file_obj in files:
            if cls.is_file_expired(file_obj):
                expired_files.append(file_obj)
                if len(expired_files) >= limit:
                    break
        
        return expired_files
    
    @classmethod
    def find_files_by_policy(cls, db: Session, policy: FileLifecyclePolicy, limit: int = 100) -> List[File]:
        """
        根据策略查找文件
        
        Args:
            db: 数据库会话
            policy: 生命周期策略
            limit: 限制返回数量
            
        Returns:
            符合策略的文件列表
        """
        # 根据策略确定路径前缀
        path_patterns = {
            FileLifecyclePolicy.RAW_FILES: "uploads/%",
            FileLifecyclePolicy.RESULT_FILES: "processed/%",
            FileLifecyclePolicy.THUMBNAIL_FILES: "thumbnails/%",
            FileLifecyclePolicy.TEMP_FILES: "temp/%"
        }
        
        pattern = path_patterns.get(policy, "%")
        
        return db.query(File).filter(
            and_(
                File.storage_path.like(pattern),
                File.status != FileStatus.DELETED
            )
        ).limit(limit).all()
    
    @classmethod
    def mark_file_for_deletion(cls, db: Session, file_obj: File, reason: str = "expired") -> bool:
        """
        标记文件为待删除状态
        
        Args:
            db: 数据库会话
            file_obj: 文件对象
            reason: 删除原因
            
        Returns:
            是否成功标记
        """
        try:
            # 更新文件状态和元数据
            metadata = file_obj.file_metadata or {}
            metadata.update({
                "marked_for_deletion": True,
                "deletion_reason": reason,
                "marked_at": datetime.now().isoformat()
            })
            
            file_crud.update_metadata(db=db, file_id=file_obj.id, metadata=metadata)
            file_crud.update_status(db=db, file_id=file_obj.id, status=FileStatus.DELETED)
            
            return True
        except Exception:
            return False
    
    @classmethod
    def cleanup_expired_files(cls, db: Session, minio_service: MinIOService, dry_run: bool = True) -> Dict[str, Any]:
        """
        清理过期文件
        
        Args:
            db: 数据库会话
            minio_service: MinIO服务
            dry_run: 是否为试运行（不实际删除）
            
        Returns:
            清理结果统计
        """
        result = {
            "total_found": 0,
            "marked_for_deletion": 0,
            "storage_cleaned": 0,
            "errors": [],
            "dry_run": dry_run,
            "processed_files": []
        }
        
        try:
            # 查找过期文件
            expired_files = cls.find_expired_files(db, limit=500)
            result["total_found"] = len(expired_files)
            
            for file_obj in expired_files:
                file_info = {
                    "id": file_obj.id,
                    "filename": file_obj.filename,
                    "storage_path": file_obj.storage_path,
                    "created_at": file_obj.created_at,
                    "policy": cls.get_file_policy(file_obj.storage_path).value
                }
                
                try:
                    if not dry_run:
                        # 标记数据库记录为删除
                        if cls.mark_file_for_deletion(db, file_obj, "lifecycle_cleanup"):
                            result["marked_for_deletion"] += 1
                            file_info["database_marked"] = True
                        
                        # 从MinIO删除文件
                        if minio_service.delete_file(file_obj.storage_path):
                            result["storage_cleaned"] += 1
                            file_info["storage_deleted"] = True
                        else:
                            file_info["storage_delete_failed"] = True
                    else:
                        file_info["would_be_deleted"] = True
                    
                    result["processed_files"].append(file_info)
                    
                except Exception as e:
                    error_info = {
                        "file_id": file_obj.id,
                        "error": str(e)
                    }
                    result["errors"].append(error_info)
            
        except Exception as e:
            result["errors"].append({"general_error": str(e)})
        
        return result
    
    @classmethod
    def get_lifecycle_statistics(cls, db: Session) -> Dict[str, Any]:
        """
        获取文件生命周期统计信息
        
        Args:
            db: 数据库会话
            
        Returns:
            统计信息
        """
        stats = {
            "total_files": 0,
            "by_policy": {},
            "expiry_summary": {
                "expired": 0,
                "expiring_soon": 0,  # 7天内过期
                "permanent": 0
            },
            "generated_at": datetime.now().isoformat()
        }
        
        # 按策略统计
        for policy in FileLifecyclePolicy:
            files = cls.find_files_by_policy(db, policy, limit=10000)
            
            policy_stats = {
                "count": len(files),
                "expired": 0,
                "expiring_soon": 0,
                "retention_days": cls.LIFECYCLE_POLICIES[policy]
            }
            
            for file_obj in files:
                if cls.is_file_expired(file_obj):
                    policy_stats["expired"] += 1
                    stats["expiry_summary"]["expired"] += 1
                elif cls.LIFECYCLE_POLICIES[policy] is not None:
                    # 检查是否即将过期（7天内）
                    expiry_date = cls.calculate_expiry_date(file_obj.created_at, policy)
                    if expiry_date and (expiry_date - datetime.now()).days <= 7:
                        policy_stats["expiring_soon"] += 1
                        stats["expiry_summary"]["expiring_soon"] += 1
                else:
                    stats["expiry_summary"]["permanent"] += 1
            
            stats["by_policy"][policy.value] = policy_stats
            stats["total_files"] += policy_stats["count"]
        
        return stats
    
    @classmethod
    def archive_old_files(cls, db: Session, minio_service: MinIOService, archive_days: int = 365) -> Dict[str, Any]:
        """
        归档旧文件（移动到归档存储）
        
        Args:
            db: 数据库会话
            minio_service: MinIO服务
            archive_days: 归档天数阈值
            
        Returns:
            归档结果
        """
        result = {
            "total_candidates": 0,
            "archived": 0,
            "errors": [],
            "archive_threshold": archive_days
        }
        
        # 查找超过归档阈值的文件
        archive_date = datetime.now() - timedelta(days=archive_days)
        
        old_files = db.query(File).filter(
            and_(
                File.created_at < archive_date,
                File.status == FileStatus.UPLOADED,
                ~File.storage_path.startswith("archived/")
            )
        ).limit(100).all()
        
        result["total_candidates"] = len(old_files)
        
        for file_obj in old_files:
            try:
                # 生成归档路径
                archive_path = f"archived/{file_obj.storage_path}"
                
                # 复制到归档位置
                if minio_service.copy_file(file_obj.storage_path, archive_path):
                    # 更新数据库记录
                    metadata = file_obj.file_metadata or {}
                    metadata.update({
                        "archived": True,
                        "archived_at": datetime.now().isoformat(),
                        "original_path": file_obj.storage_path,
                        "archive_path": archive_path
                    })
                    
                    file_crud.update_metadata(db=db, file_id=file_obj.id, metadata=metadata)
                    
                    # 更新存储路径
                    from app.schemas.file import FileUpdate
                    file_update = FileUpdate(storage_path=archive_path)
                    file_crud.update(db=db, db_obj=file_obj, obj_in=file_update)
                    
                    result["archived"] += 1
                
            except Exception as e:
                result["errors"].append({
                    "file_id": file_obj.id,
                    "error": str(e)
                })
        
        return result
