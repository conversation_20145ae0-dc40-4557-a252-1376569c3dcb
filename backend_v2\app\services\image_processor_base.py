"""
图像处理基础服务
提供统一的图像处理基础架构，包括文件下载/上传、参数转换、进度报告等
"""
import os
import tempfile
import time
from typing import Dict, Any, Optional, Tuple
from datetime import datetime, timedelta
import cv2
import numpy as np
from sqlalchemy.orm import Session
import structlog

from app.services.minio_service import MinIOService
from app.services.file_metadata_service import FileMetadataService
from app.crud.crud_file import file as file_crud
from app.models.file import File, FileStatus, FileType
from app.schemas.file import FileCreate
from app.utils.storage_path import StoragePathManager, StorageType

logger = structlog.get_logger()


class ImageProcessorBase:
    """图像处理基础类"""
    
    def __init__(self, minio_service: MinIOService, db: Session):
        """
        初始化图像处理器
        
        Args:
            minio_service: MinIO服务实例
            db: 数据库会话
        """
        self.minio_service = minio_service
        self.db = db
        self.temp_files = []  # 跟踪临时文件，用于清理
        
    def __enter__(self):
        return self
        
    def __exit__(self, exc_type, exc_val, exc_tb):
        """清理临时文件"""
        self.cleanup_temp_files()
    
    def cleanup_temp_files(self):
        """清理临时文件"""
        for temp_file in self.temp_files:
            try:
                if os.path.exists(temp_file):
                    os.remove(temp_file)
                    logger.debug("temp_file_cleaned", file=temp_file)
            except Exception as e:
                logger.warning("temp_file_cleanup_failed", file=temp_file, error=str(e))
        self.temp_files.clear()
    
    def download_image_from_minio(self, file_obj: File) -> Tuple[np.ndarray, str]:
        """
        从MinIO下载图像文件并加载为OpenCV格式
        
        Args:
            file_obj: 文件对象
            
        Returns:
            (图像数据, 临时文件路径)
            
        Raises:
            ValueError: 文件下载或加载失败
        """
        try:
            logger.info("downloading_image_from_minio", 
                       file_id=file_obj.id, 
                       storage_path=file_obj.storage_path)
            
            # 从MinIO获取文件数据
            file_data = self.minio_service.get_file_data(str(file_obj.storage_path))
            if not file_data:
                raise ValueError(f"Failed to download file from MinIO: {file_obj.storage_path}")
            
            # 创建临时文件
            temp_fd, temp_path = tempfile.mkstemp(suffix=f"_{file_obj.filename}")
            self.temp_files.append(temp_path)
            
            try:
                # 写入临时文件
                with os.fdopen(temp_fd, 'wb') as temp_file:
                    temp_file.write(file_data)
                
                # 使用OpenCV加载图像
                image = cv2.imread(temp_path)
                if image is None:
                    raise ValueError(f"Failed to load image with OpenCV: {temp_path}")
                
                logger.info("image_downloaded_successfully", 
                           file_id=file_obj.id,
                           image_shape=image.shape,
                           temp_path=temp_path)
                
                return image, temp_path
                
            except Exception as e:
                # 如果加载失败，清理临时文件
                try:
                    os.close(temp_fd)
                except:
                    pass
                raise e
                
        except Exception as e:
            logger.error("image_download_failed", 
                        file_id=file_obj.id,
                        error=str(e))
            raise ValueError(f"Failed to download and load image: {str(e)}")
    
    def upload_processed_image(
        self, 
        processed_image: np.ndarray, 
        original_file: File,
        operation_name: str,
        parameters: Dict[str, Any],
        processing_time: float
    ) -> Dict[str, Any]:
        """
        上传处理后的图像到MinIO并创建数据库记录
        
        Args:
            processed_image: 处理后的图像数据
            original_file: 原始文件对象
            operation_name: 操作名称
            parameters: 处理参数
            processing_time: 处理耗时
            
        Returns:
            包含结果信息的字典
        """
        try:
            logger.info("uploading_processed_image",
                       original_file_id=original_file.id,
                       operation=operation_name)
            
            # 生成输出文件名
            base_name = os.path.splitext(original_file.filename)[0]
            ext = os.path.splitext(original_file.filename)[1]
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_filename = f"{operation_name}_{base_name}_{timestamp}{ext}"
            
            # 创建临时文件保存处理结果
            temp_fd, temp_output_path = tempfile.mkstemp(suffix=f"_{output_filename}")
            self.temp_files.append(temp_output_path)
            
            try:
                os.close(temp_fd)  # 关闭文件描述符，让cv2.imwrite使用
                
                # 保存处理后的图像
                success = cv2.imwrite(temp_output_path, processed_image)
                if not success:
                    raise ValueError("Failed to save processed image")
                
                # 生成存储路径
                storage_path = StoragePathManager.generate_path(
                    storage_type=StorageType.RESULT,
                    filename=output_filename,
                    task_id=getattr(original_file, 'task_id', None),
                    batch_id=getattr(original_file, 'batch_id', None)
                )
                
                # 上传到MinIO
                with open(temp_output_path, 'rb') as f:
                    file_data = f.read()
                
                upload_success = self.minio_service.upload_file_data(
                    object_name=storage_path,
                    file_data=file_data,
                    content_type="image/jpeg"  # 默认JPEG，可以根据扩展名调整
                )
                
                if not upload_success:
                    raise ValueError("Failed to upload processed image to MinIO")
                
                # 计算文件大小和校验和
                file_size = len(file_data)
                checksum = FileMetadataService.calculate_checksum(file_data)
                
                # 创建数据库记录
                file_create = FileCreate(
                    filename=output_filename,
                    original_filename=output_filename,
                    file_type=FileType.IMAGE,
                    content_type="image/jpeg",
                    storage_path=storage_path,
                    storage_bucket=self.minio_service.bucket_name,
                    file_size=file_size,
                    checksum=checksum,
                    file_metadata={
                        "operation": operation_name,
                        "parameters": parameters,
                        "processing_time": processing_time,
                        "original_file_id": original_file.id,
                        "image_shape": processed_image.shape[:2][::-1],  # (width, height)
                        "created_at": datetime.now().isoformat()
                    },
                    batch_id=getattr(original_file, 'batch_id', None),
                    upload_session_id=None
                )
                
                # 保存到数据库
                result_file = file_crud.create(db=self.db, obj_in=file_create)
                
                # 更新文件状态为已上传
                result_file.status = FileStatus.UPLOADED
                self.db.commit()
                
                # 生成预签名下载URL
                download_url = self.minio_service.generate_presigned_get_url(
                    object_name=storage_path,
                    expires=timedelta(hours=24)
                )
                
                result = {
                    "success": True,
                    "output_file_id": result_file.id,
                    "output_filename": output_filename,
                    "output_path": storage_path,
                    "minio_url": download_url,
                    "file_size": file_size,
                    "checksum": checksum,
                    "processing_time": processing_time,
                    "metadata": {
                        "operation": operation_name,
                        "parameters": parameters,
                        "original_file_id": original_file.id,
                        "image_dimensions": processed_image.shape[:2][::-1]
                    }
                }
                
                logger.info("processed_image_uploaded_successfully",
                           original_file_id=original_file.id,
                           result_file_id=result_file.id,
                           output_filename=output_filename)
                
                return result
                
            except Exception as e:
                # 清理临时文件
                try:
                    if os.path.exists(temp_output_path):
                        os.remove(temp_output_path)
                except:
                    pass
                raise e
                
        except Exception as e:
            logger.error("processed_image_upload_failed",
                        original_file_id=original_file.id,
                        operation=operation_name,
                        error=str(e))
            raise ValueError(f"Failed to upload processed image: {str(e)}")
    
    def update_task_progress(self, task_id: str, current: int, total: int, status: str):
        """
        更新任务进度
        
        Args:
            task_id: 任务ID
            current: 当前进度
            total: 总进度
            status: 状态描述
        """
        try:
            # 这里可以集成到Celery的update_state或任务状态系统
            logger.info("task_progress_updated",
                       task_id=task_id,
                       current=current,
                       total=total,
                       status=status,
                       progress_percent=round((current / total) * 100, 1))
        except Exception as e:
            logger.warning("task_progress_update_failed",
                          task_id=task_id,
                          error=str(e))
    
    def process_image(self, image: np.ndarray, parameters: Dict[str, Any]) -> np.ndarray:
        """
        处理图像的抽象方法，子类需要实现
        
        Args:
            image: 输入图像
            parameters: 处理参数
            
        Returns:
            处理后的图像
        """
        raise NotImplementedError("Subclasses must implement process_image method")
    
    def execute_image_processing(
        self,
        file_obj: File,
        parameters: Dict[str, Any],
        operation_name: str,
        task_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        执行完整的图像处理流程
        
        Args:
            file_obj: 输入文件对象
            parameters: 处理参数
            operation_name: 操作名称
            task_id: 任务ID（可选）
            
        Returns:
            处理结果字典
        """
        start_time = time.time()
        
        try:
            # 更新进度：开始下载
            if task_id:
                self.update_task_progress(task_id, 10, 100, "Downloading image...")
            
            # 下载图像
            image, temp_path = self.download_image_from_minio(file_obj)
            
            # 更新进度：开始处理
            if task_id:
                self.update_task_progress(task_id, 30, 100, f"Processing image with {operation_name}...")
            
            # 处理图像
            processed_image = self.process_image(image, parameters)
            
            # 更新进度：开始上传
            if task_id:
                self.update_task_progress(task_id, 70, 100, "Uploading result...")
            
            # 计算处理时间
            processing_time = time.time() - start_time
            
            # 上传结果
            result = self.upload_processed_image(
                processed_image=processed_image,
                original_file=file_obj,
                operation_name=operation_name,
                parameters=parameters,
                processing_time=processing_time
            )
            
            # 更新进度：完成
            if task_id:
                self.update_task_progress(task_id, 100, 100, "Processing completed")
            
            logger.info("image_processing_completed",
                       file_id=file_obj.id,
                       operation=operation_name,
                       processing_time=processing_time)
            
            return result
            
        except Exception as e:
            logger.error("image_processing_failed",
                        file_id=file_obj.id,
                        operation=operation_name,
                        error=str(e))
            raise
        finally:
            # 确保清理临时文件
            self.cleanup_temp_files()


class ParameterConverter:
    """参数转换器，处理新旧格式的兼容性"""
    
    @staticmethod
    def convert_legacy_parameters(operation: str, legacy_params: Dict[str, Any]) -> Dict[str, Any]:
        """
        将旧格式参数转换为新格式
        
        Args:
            operation: 操作名称
            legacy_params: 旧格式参数
            
        Returns:
            新格式参数
        """
        # 大部分参数可以直接使用，这里处理特殊情况
        converted_params = legacy_params.copy()
        
        # 处理特定操作的参数转换
        if operation == "sharpen":
            # 确保intensity参数存在，但不自动修正无效值
            if "intensity" not in converted_params:
                converted_params["intensity"] = 1.0
            else:
                # 只进行类型转换，不修正范围
                converted_params["intensity"] = float(converted_params["intensity"])
        
        elif operation == "gamma_correction":
            # 确保gamma参数存在，但不自动修正无效值
            if "gamma" not in converted_params:
                converted_params["gamma"] = 1.0
            else:
                # 只进行类型转换，不修正范围
                converted_params["gamma"] = float(converted_params["gamma"])
        
        elif operation == "edge_detection":
            # 确保阈值参数存在
            if "low_threshold" not in converted_params:
                converted_params["low_threshold"] = 50
            if "high_threshold" not in converted_params:
                converted_params["high_threshold"] = 150
            
            # 确保阈值在合理范围内
            converted_params["low_threshold"] = max(0, min(255, int(converted_params["low_threshold"])))
            converted_params["high_threshold"] = max(0, min(255, int(converted_params["high_threshold"])))
        
        return converted_params
    
    @staticmethod
    def validate_parameters(operation: str, parameters: Dict[str, Any]) -> Tuple[bool, str]:
        """
        验证参数的有效性
        
        Args:
            operation: 操作名称
            parameters: 参数字典
            
        Returns:
            (是否有效, 错误信息)
        """
        try:
            # 基本验证
            if not isinstance(parameters, dict):
                return False, "Parameters must be a dictionary"
            
            # 操作特定验证
            if operation == "sharpen":
                intensity = parameters.get("intensity", 1.0)
                if not isinstance(intensity, (int, float)) or not (0.1 <= intensity <= 5.0):
                    return False, "Intensity must be between 0.1 and 5.0"
            
            elif operation == "gamma_correction":
                gamma = parameters.get("gamma", 1.0)
                if not isinstance(gamma, (int, float)) or not (0.1 <= gamma <= 3.0):
                    return False, "Gamma must be between 0.1 and 3.0"
            
            elif operation == "edge_detection":
                low_threshold = parameters.get("low_threshold", 50)
                high_threshold = parameters.get("high_threshold", 150)
                
                if not isinstance(low_threshold, int) or not (0 <= low_threshold <= 255):
                    return False, "Low threshold must be an integer between 0 and 255"
                
                if not isinstance(high_threshold, int) or not (0 <= high_threshold <= 255):
                    return False, "High threshold must be an integer between 0 and 255"
                
                if low_threshold >= high_threshold:
                    return False, "Low threshold must be less than high threshold"
            
            return True, "Parameters are valid"
            
        except Exception as e:
            return False, f"Parameter validation error: {str(e)}"
