#!/usr/bin/env python3
"""
测试文件元数据管理功能
"""
import sys
import os
import io
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from fastapi.testclient import TestClient
from app.main import app
from app.core.database import get_db_session
from app.crud.crud_file import file as file_crud
from app.services.file_metadata_service import FileMetadataService

def test_file_deduplication():
    """测试文件去重功能"""
    print("🧪 测试文件去重功能...")
    
    try:
        client = TestClient(app)
        db = get_db_session()
        
        # 上传第一个文件
        test_file_content = b"This is a test file for deduplication"
        test_file1 = io.BytesIO(test_file_content)
        
        files1 = {
            "file": ("duplicate_test.jpg", test_file1, "image/jpeg")
        }
        
        print("🔄 上传第一个文件...")
        response1 = client.post("/api/files/upload", files=files1)
        
        if response1.status_code != 200:
            print(f"❌ 第一个文件上传失败: {response1.status_code}")
            return False
        
        result1 = response1.json()
        file1_db_id = result1.get('database_id')
        print(f"✅ 第一个文件上传成功，数据库ID: {file1_db_id}")
        
        # 上传相同内容的第二个文件
        test_file2 = io.BytesIO(test_file_content)
        files2 = {
            "file": ("duplicate_test_copy.jpg", test_file2, "image/jpeg")
        }
        
        print("🔄 上传相同内容的第二个文件...")
        response2 = client.post("/api/files/upload", files=files2)
        
        if response2.status_code != 200:
            print(f"❌ 第二个文件上传失败: {response2.status_code}")
            return False
        
        result2 = response2.json()
        file2_db_id = result2.get('database_id')
        print(f"✅ 第二个文件上传成功，数据库ID: {file2_db_id}")
        
        # 测试重复检测API
        print(f"🔄 检测文件 {file1_db_id} 的重复项...")
        dup_response = client.get(f"/api/files/{file1_db_id}/duplicates")
        
        print(f"📡 重复检测响应状态码: {dup_response.status_code}")
        
        if dup_response.status_code == 200:
            dup_result = dup_response.json()
            print(f"✅ 重复检测成功!")
            print(f"🔍 发现 {dup_result.get('duplicate_count', 0)} 个重复文件")
            
            duplicates = dup_result.get('duplicates', [])
            for dup in duplicates:
                print(f"   - 重复文件ID: {dup.get('id')}, 匹配类型: {dup.get('match_type')}")
            
            return dup_result.get('duplicate_count', 0) > 0
        else:
            print(f"❌ 重复检测失败: {dup_response.status_code}")
            print(f"错误详情: {dup_response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        if 'db' in locals():
            db.close()

def test_file_statistics():
    """测试文件统计功能"""
    print("\n🧪 测试文件统计功能...")
    
    try:
        client = TestClient(app)
        
        print("🔄 获取总体文件统计...")
        stats_response = client.get("/api/files/metadata/statistics")
        
        print(f"📡 统计响应状态码: {stats_response.status_code}")
        
        if stats_response.status_code == 200:
            stats = stats_response.json()
            print(f"✅ 统计获取成功!")
            print(f"📊 总文件数: {stats.get('total_files', 0)}")
            print(f"📏 总大小: {stats.get('total_size_mb', 0)} MB")
            
            type_dist = stats.get('type_distribution', {})
            print(f"📂 文件类型分布:")
            for file_type, count in type_dist.items():
                print(f"   - {file_type}: {count} 个文件")
            
            status_dist = stats.get('status_distribution', {})
            print(f"📊 状态分布:")
            for status, count in status_dist.items():
                print(f"   - {status}: {count} 个文件")
            
            return True
        else:
            print(f"❌ 统计获取失败: {stats_response.status_code}")
            print(f"错误详情: {stats_response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        return False

def test_file_metadata_service():
    """测试文件元数据服务"""
    print("\n🧪 测试文件元数据服务...")
    
    try:
        # 测试校验和计算
        test_data = b"Hello, World!"
        checksum = FileMetadataService.calculate_file_checksum(test_data)
        print(f"✅ 校验和计算: {checksum}")
        
        # 测试路径组织
        object_name, file_id = FileMetadataService.organize_file_path(
            filename="test.jpg",
            task_id="task123",
            batch_id=456
        )
        print(f"✅ 智能路径生成: {object_name}")
        print(f"🆔 文件ID: {file_id}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success1 = test_file_deduplication()
    success2 = test_file_statistics()
    success3 = test_file_metadata_service()
    
    if success1 and success2 and success3:
        print("\n🎉 所有文件元数据管理测试通过！")
        sys.exit(0)
    else:
        print("\n💥 文件元数据管理测试失败！")
        sys.exit(1)
