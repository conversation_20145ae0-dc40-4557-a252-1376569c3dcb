"""
美颜增强处理器
实现人脸美颜功能，包括磨皮和瘦脸效果
"""
from typing import Dict, Any, Optional, Tuple
import cv2
import numpy as np
import structlog

from app.services.image_processor_base import ImageProcessorBase, ParameterConverter

logger = structlog.get_logger()


class BeautyEnhancementProcessor(ImageProcessorBase):
    """美颜增强处理器"""
    
    def process_image(self, image: np.ndarray, parameters: Dict[str, Any]) -> np.ndarray:
        """
        对图像进行美颜处理
        
        Args:
            image: 输入图像（BGR格式）
            parameters: 处理参数
                - smoothing_strength: 磨皮强度，范围0.0-1.0，默认0.5
                - slimming_strength: 瘦脸强度，范围0.0-1.0，默认0.3
                
        Returns:
            美颜处理后的图像
        """
        try:
            smoothing_strength = parameters.get('smoothing_strength', 0.5)
            slimming_strength = parameters.get('slimming_strength', 0.3)
            
            logger.info("processing_beauty_enhancement", 
                       input_shape=image.shape,
                       smoothing_strength=smoothing_strength,
                       slimming_strength=slimming_strength)
            
            result = image.copy()
            
            # 检测人脸
            face_rect = self.detect_face(image)
            
            if face_rect is not None:
                logger.info("face_detected", face_rect=face_rect)
            else:
                logger.info("no_face_detected", fallback="applying_global_enhancement")
            
            # 应用瘦脸效果
            if slimming_strength > 0:
                result = self.apply_face_slimming(result, face_rect, slimming_strength)
            
            # 应用磨皮效果
            if smoothing_strength > 0:
                result = self.apply_skin_smoothing(result, face_rect, smoothing_strength)
            
            logger.info("beauty_enhancement_completed",
                       output_shape=result.shape,
                       face_detected=face_rect is not None)
            
            return result
            
        except Exception as e:
            logger.error("beauty_enhancement_failed", 
                        parameters=parameters,
                        error=str(e))
            raise ValueError(f"Beauty enhancement failed: {str(e)}")
    
    def detect_face(self, image: np.ndarray) -> Optional[Tuple[int, int, int, int]]:
        """
        检测人脸位置
        
        Args:
            image: 输入图像
            
        Returns:
            人脸矩形 (x, y, w, h) 或 None
        """
        try:
            # 转换为灰度图
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            
            # 使用Haar级联分类器检测人脸
            # 注意：这里使用OpenCV内置的分类器，实际部署时可能需要确保文件存在
            try:
                face_cascade = cv2.CascadeClassifier(cv2.data.haarcascades + 'haarcascade_frontalface_default.xml')
                faces = face_cascade.detectMultiScale(gray, 1.1, 4)
                
                if len(faces) > 0:
                    # 返回最大的人脸
                    largest_face = max(faces, key=lambda f: f[2] * f[3])
                    return tuple(largest_face)
                
            except Exception as e:
                logger.warning("haar_cascade_detection_failed", error=str(e))
            
            return None
            
        except Exception as e:
            logger.error("face_detection_failed", error=str(e))
            return None
    
    def apply_face_slimming(
        self, 
        image: np.ndarray, 
        face_rect: Optional[Tuple[int, int, int, int]], 
        strength: float
    ) -> np.ndarray:
        """
        应用瘦脸效果
        
        Args:
            image: 输入图像
            face_rect: 人脸矩形区域
            strength: 瘦脸强度
            
        Returns:
            瘦脸处理后的图像
        """
        if strength <= 0:
            return image
        
        result = image.copy()
        
        if face_rect is not None:
            x, y, w, h = face_rect
            
            # 计算人脸中心点
            center_x = x + w // 2
            center_y = y + h // 2
            
            # 创建瘦脸变换
            # 这是一个简化的瘦脸算法，实际应用中可能需要更复杂的面部关键点检测
            radius = min(w, h) // 2
            
            # 应用局部收缩变换
            result = self.apply_local_warp(result, center_x, center_y, radius, strength * 0.3)
            
            logger.info("face_slimming_applied", 
                       face_rect=face_rect,
                       strength=strength)
        else:
            logger.info("face_slimming_skipped", reason="no_face_detected")
        
        return result
    
    def apply_local_warp(
        self, 
        image: np.ndarray, 
        center_x: int, 
        center_y: int, 
        radius: int, 
        strength: float
    ) -> np.ndarray:
        """
        应用局部变形（瘦脸效果）
        
        Args:
            image: 输入图像
            center_x: 变形中心X坐标
            center_y: 变形中心Y坐标
            radius: 影响半径
            strength: 变形强度
            
        Returns:
            变形后的图像
        """
        height, width = image.shape[:2]
        result = image.copy()
        
        # 创建网格
        y_indices, x_indices = np.mgrid[0:height, 0:width]
        
        # 计算到中心的距离
        dx = x_indices - center_x
        dy = y_indices - center_y
        distance = np.sqrt(dx**2 + dy**2)
        
        # 创建变形掩码
        mask = distance < radius
        
        # 应用收缩变换
        scale_factor = 1.0 - strength * (1.0 - distance[mask] / radius)
        
        # 计算新的坐标
        new_x = center_x + dx[mask] * scale_factor
        new_y = center_y + dy[mask] * scale_factor
        
        # 确保坐标在有效范围内
        new_x = np.clip(new_x, 0, width - 1).astype(int)
        new_y = np.clip(new_y, 0, height - 1).astype(int)
        
        # 应用变形
        y_mask, x_mask = np.where(mask)
        result[y_mask, x_mask] = image[new_y, new_x]
        
        return result
    
    def apply_skin_smoothing(
        self, 
        image: np.ndarray, 
        face_rect: Optional[Tuple[int, int, int, int]], 
        strength: float
    ) -> np.ndarray:
        """
        应用磨皮效果
        
        Args:
            image: 输入图像
            face_rect: 人脸矩形区域
            strength: 磨皮强度
            
        Returns:
            磨皮处理后的图像
        """
        if strength <= 0:
            return image
        
        result = image.copy()
        
        # 确定处理区域
        if face_rect is not None:
            x, y, w, h = face_rect
            # 扩展处理区域
            x = max(0, x - int(w * 0.1))
            y = max(0, y - int(h * 0.1))
            w = min(image.shape[1] - x, int(w * 1.2))
            h = min(image.shape[0] - y, int(h * 1.2))
            
            face_region = result[y:y+h, x:x+w]
        else:
            # 如果没有检测到人脸，处理整个图像
            face_region = result
            x, y = 0, 0
        
        # 双边滤波参数
        d = int(15 + strength * 20)  # 滤波直径
        sigma_color = 80 + strength * 100  # 颜色相似性
        sigma_space = 80 + strength * 100  # 空间相似性
        
        # 应用双边滤波
        smoothed = cv2.bilateralFilter(face_region, d, sigma_color, sigma_space)
        
        # 边缘保护
        gray = cv2.cvtColor(face_region, cv2.COLOR_BGR2GRAY)
        edges = cv2.Canny(gray, 50, 150)
        edges = cv2.dilate(edges, np.ones((3,3), np.uint8), iterations=1)
        edges_3ch = cv2.cvtColor(edges, cv2.COLOR_GRAY2BGR) / 255.0
        
        # 在边缘区域保留更多原图细节
        edge_protected = smoothed * (1 - edges_3ch) + face_region * edges_3ch
        edge_protected = edge_protected.astype(np.uint8)
        
        # 混合原图和处理后的图像
        alpha = 0.3 + strength * 0.6  # 混合强度
        blended = cv2.addWeighted(face_region, 1 - alpha, edge_protected, alpha, 0)
        
        # 将处理后的区域放回原图
        if face_rect is not None:
            result[y:y+h, x:x+w] = blended
        else:
            result = blended
        
        logger.info("skin_smoothing_applied", 
                   strength=strength,
                   face_detected=face_rect is not None)
        
        return result


def create_beauty_enhancement_processor(minio_service, db_session) -> BeautyEnhancementProcessor:
    """
    创建美颜增强处理器实例
    
    Args:
        minio_service: MinIO服务实例
        db_session: 数据库会话
        
    Returns:
        美颜增强处理器实例
    """
    return BeautyEnhancementProcessor(minio_service, db_session)


def validate_beauty_enhancement_parameters(parameters: Dict[str, Any]) -> Dict[str, Any]:
    """
    验证和转换美颜增强处理参数
    
    Args:
        parameters: 输入参数
        
    Returns:
        验证后的参数
        
    Raises:
        ValueError: 参数无效时抛出
    """
    try:
        # 提取参数
        smoothing_strength = parameters.get('smoothing_strength', 0.5)
        slimming_strength = parameters.get('slimming_strength', 0.3)
        
        # 类型转换
        try:
            smoothing_strength = float(smoothing_strength)
            slimming_strength = float(slimming_strength)
        except (ValueError, TypeError):
            raise ValueError("Strength values must be numbers")
        
        # 范围检查
        if not (0.0 <= smoothing_strength <= 1.0):
            raise ValueError("Smoothing strength must be between 0.0 and 1.0")
        
        if not (0.0 <= slimming_strength <= 1.0):
            raise ValueError("Slimming strength must be between 0.0 and 1.0")
        
        return {
            'smoothing_strength': smoothing_strength,
            'slimming_strength': slimming_strength
        }
        
    except Exception as e:
        logger.error("beauty_enhancement_parameter_validation_failed", 
                    parameters=parameters,
                    error=str(e))
        raise ValueError(f"Parameter validation failed: {str(e)}")


# 为了向后兼容，提供旧格式的函数接口
def beauty_enhancement_legacy(image_path: str, smoothing_strength: float = 0.5, slimming_strength: float = 0.3) -> str:
    """
    旧格式的美颜增强函数接口（向后兼容）
    
    Args:
        image_path: 图像文件路径
        smoothing_strength: 磨皮强度
        slimming_strength: 瘦脸强度
        
    Returns:
        处理后的图像路径
        
    Note:
        这个函数仅用于向后兼容，新代码应使用BeautyEnhancementProcessor
    """
    logger.warning("using_legacy_beauty_enhancement_interface", 
                  image_path=image_path,
                  smoothing_strength=smoothing_strength,
                  slimming_strength=slimming_strength)
    
    # 这里可以实现向旧接口的适配逻辑
    # 但在新架构中，建议直接使用BeautyEnhancementProcessor
    raise NotImplementedError("Legacy interface not implemented in new architecture. Use BeautyEnhancementProcessor instead.")
