version: '3.8'

services:
  # PostgreSQL数据库
  postgres:
    image: postgres:15-alpine
    container_name: clover_postgres_v2
    environment:
      POSTGRES_DB: clover_v2
      POSTGRES_USER: clover
      POSTGRES_PASSWORD: clover_password
    ports:
      - "5433:5432"  # 避免与现有数据库冲突
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init-db.sql:/docker-entrypoint-initdb.d/init-db.sql
    networks:
      - clover_network

  # Redis缓存和消息队列
  redis:
    image: redis:7-alpine
    container_name: clover_redis_v2
    ports:
      - "6380:6379"  # 避免与现有Redis冲突
    volumes:
      - redis_data:/data
    networks:
      - clover_network

  # MinIO对象存储
  minio:
    image: minio/minio:latest
    container_name: clover_minio_v2
    environment:
      MINIO_ROOT_USER: minioadmin
      MINIO_ROOT_PASSWORD: minioadmin123
    ports:
      - "9001:9000"  # API端口
      - "9002:9001"  # 控制台端口
    volumes:
      - minio_data:/data
    command: server /data --console-address ":9001"
    networks:
      - clover_network



volumes:
  postgres_data:
  redis_data:
  minio_data:

networks:
  clover_network:
    driver: bridge
