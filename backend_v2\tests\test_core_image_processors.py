#!/usr/bin/env python3
"""
测试核心图像处理器
验证2.3.2任务的核心图像处理功能
"""
import sys
import os
import numpy as np
import cv2
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_sharpen_processor():
    """测试锐化处理器"""
    print("🧪 测试锐化处理器...")
    
    try:
        from app.services.image_processors.sharpen_processor import SharpenProcessor, validate_sharpen_parameters
        from app.services.minio_service import get_minio_service
        from app.core.database import get_db_session
        
        # 创建测试图像
        test_image = np.random.randint(0, 255, (100, 100, 3), dtype=np.uint8)
        
        # 创建处理器
        db = get_db_session()
        minio_service = get_minio_service()
        
        try:
            processor = SharpenProcessor(minio_service, db)
            
            # 测试默认参数
            result = processor.process_image(test_image, {})
            
            if result.shape == test_image.shape:
                print("✅ 锐化处理器输出形状正确")
            else:
                print(f"❌ 输出形状错误: {result.shape}")
                return False
            
            # 测试不同强度
            for intensity in [0.5, 1.0, 2.0]:
                params = {"intensity": intensity}
                validated_params = validate_sharpen_parameters(params)
                result = processor.process_image(test_image, validated_params)
                
                if result.shape == test_image.shape:
                    print(f"✅ 强度 {intensity} 处理成功")
                else:
                    print(f"❌ 强度 {intensity} 处理失败")
                    return False
            
            # 测试参数验证
            try:
                validate_sharpen_parameters({"intensity": -1})
                print("❌ 负强度参数未被拒绝")
                return False
            except ValueError:
                print("✅ 负强度参数正确被拒绝")
            
            return True
            
        finally:
            db.close()
        
    except Exception as e:
        print(f"❌ 锐化处理器测试失败: {str(e)}")
        return False

def test_edge_detection_processor():
    """测试边缘检测处理器"""
    print("\n🧪 测试边缘检测处理器...")
    
    try:
        from app.services.image_processors.edge_detection_processor import EdgeDetectionProcessor, validate_edge_detection_parameters
        from app.services.minio_service import get_minio_service
        from app.core.database import get_db_session
        
        # 创建测试图像（带一些结构）
        test_image = np.zeros((100, 100, 3), dtype=np.uint8)
        cv2.rectangle(test_image, (20, 20), (80, 80), (255, 255, 255), -1)
        
        # 创建处理器
        db = get_db_session()
        minio_service = get_minio_service()
        
        try:
            processor = EdgeDetectionProcessor(minio_service, db)
            
            # 测试默认参数
            result = processor.process_image(test_image, {})
            
            if result.shape == test_image.shape:
                print("✅ 边缘检测处理器输出形状正确")
            else:
                print(f"❌ 输出形状错误: {result.shape}")
                return False
            
            # 检查是否检测到边缘（应该有白色像素）
            if np.sum(result > 0) > 0:
                print("✅ 成功检测到边缘")
            else:
                print("❌ 未检测到边缘")
                return False
            
            # 测试不同阈值
            params = {"low_threshold": 30, "high_threshold": 120}
            validated_params = validate_edge_detection_parameters(params)
            result = processor.process_image(test_image, validated_params)
            
            if result.shape == test_image.shape:
                print("✅ 自定义阈值处理成功")
            else:
                print("❌ 自定义阈值处理失败")
                return False
            
            # 测试参数验证
            try:
                validate_edge_detection_parameters({"low_threshold": 200, "high_threshold": 100})
                print("❌ 无效阈值参数未被拒绝")
                return False
            except ValueError:
                print("✅ 无效阈值参数正确被拒绝")
            
            return True
            
        finally:
            db.close()
        
    except Exception as e:
        print(f"❌ 边缘检测处理器测试失败: {str(e)}")
        return False

def test_gamma_correction_processor():
    """测试伽马校正处理器"""
    print("\n🧪 测试伽马校正处理器...")
    
    try:
        from app.services.image_processors.gamma_correction_processor import GammaCorrectionProcessor, validate_gamma_correction_parameters
        from app.services.minio_service import get_minio_service
        from app.core.database import get_db_session
        
        # 创建测试图像
        test_image = np.full((100, 100, 3), 128, dtype=np.uint8)  # 中等亮度
        
        # 创建处理器
        db = get_db_session()
        minio_service = get_minio_service()
        
        try:
            processor = GammaCorrectionProcessor(minio_service, db)
            
            # 测试默认参数（gamma=1.0，应该无变化）
            result = processor.process_image(test_image, {"gamma": 1.0})
            
            if result.shape == test_image.shape:
                print("✅ 伽马校正处理器输出形状正确")
            else:
                print(f"❌ 输出形状错误: {result.shape}")
                return False
            
            # 测试变亮（gamma < 1.0）
            params = {"gamma": 0.5}
            validated_params = validate_gamma_correction_parameters(params)
            result_bright = processor.process_image(test_image, validated_params)
            
            if np.mean(result_bright) > np.mean(test_image):
                print("✅ 伽马 < 1.0 正确使图像变亮")
            else:
                print("❌ 伽马 < 1.0 未使图像变亮")
                return False
            
            # 测试变暗（gamma > 1.0）
            params = {"gamma": 2.0}
            validated_params = validate_gamma_correction_parameters(params)
            result_dark = processor.process_image(test_image, validated_params)
            
            if np.mean(result_dark) < np.mean(test_image):
                print("✅ 伽马 > 1.0 正确使图像变暗")
            else:
                print("❌ 伽马 > 1.0 未使图像变暗")
                return False
            
            # 测试参数验证
            try:
                validate_gamma_correction_parameters({"gamma": 0})
                print("❌ 零伽马参数未被拒绝")
                return False
            except ValueError:
                print("✅ 零伽马参数正确被拒绝")
            
            return True
            
        finally:
            db.close()
        
    except Exception as e:
        print(f"❌ 伽马校正处理器测试失败: {str(e)}")
        return False

def test_task_imports():
    """测试任务导入"""
    print("\n🧪 测试任务导入...")
    
    try:
        from app.tasks.image_tasks import (
            process_image_sharpen,
            process_image_edge_detection,
            process_image_gamma_correction
        )
        
        # 检查任务是否正确注册
        tasks = [
            ("锐化", process_image_sharpen),
            ("边缘检测", process_image_edge_detection),
            ("伽马校正", process_image_gamma_correction)
        ]
        
        for name, task in tasks:
            if hasattr(task, 'name'):
                print(f"✅ {name}任务已注册: {task.name}")
            else:
                print(f"❌ {name}任务未正确注册")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ 任务导入测试失败: {str(e)}")
        return False

def test_parameter_compatibility():
    """测试参数兼容性"""
    print("\n🧪 测试参数兼容性...")
    
    try:
        from app.services.image_processors import (
            validate_sharpen_parameters,
            validate_edge_detection_parameters,
            validate_gamma_correction_parameters
        )
        
        # 测试旧格式参数
        legacy_sharpen = {"intensity": "2.0"}  # 字符串格式
        validated = validate_sharpen_parameters(legacy_sharpen)
        if validated["intensity"] == 2.0:
            print("✅ 锐化参数字符串转换成功")
        else:
            print("❌ 锐化参数字符串转换失败")
            return False
        
        # 测试边缘检测参数
        legacy_edge = {"low_threshold": "40", "high_threshold": "140"}
        validated = validate_edge_detection_parameters(legacy_edge)
        if validated["low_threshold"] == 40 and validated["high_threshold"] == 140:
            print("✅ 边缘检测参数转换成功")
        else:
            print("❌ 边缘检测参数转换失败")
            return False
        
        # 测试伽马校正参数
        legacy_gamma = {"gamma": "1.5"}
        validated = validate_gamma_correction_parameters(legacy_gamma)
        if validated["gamma"] == 1.5:
            print("✅ 伽马校正参数转换成功")
        else:
            print("❌ 伽马校正参数转换失败")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 参数兼容性测试失败: {str(e)}")
        return False

if __name__ == "__main__":
    print("🚀 开始核心图像处理器测试...")
    
    success1 = test_sharpen_processor()
    success2 = test_edge_detection_processor()
    success3 = test_gamma_correction_processor()
    success4 = test_task_imports()
    success5 = test_parameter_compatibility()
    
    if success1 and success2 and success3 and success4 and success5:
        print("\n🎉 所有核心图像处理器测试通过！")
        print("\n📋 2.3.2任务验证完成:")
        print("✅ 锐化处理器：正确实现图像锐化算法，支持强度调节")
        print("✅ 边缘检测处理器：正确实现Canny边缘检测，支持阈值调节")
        print("✅ 伽马校正处理器：正确实现亮度调节，支持变亮/变暗")
        print("✅ 任务集成：所有Celery任务正确注册和导入")
        print("✅ 参数兼容性：新旧格式参数转换正常")
        print("\n🎯 核心图像处理任务迁移完成！")
        sys.exit(0)
    else:
        print("\n💥 核心图像处理器测试失败！")
        sys.exit(1)
