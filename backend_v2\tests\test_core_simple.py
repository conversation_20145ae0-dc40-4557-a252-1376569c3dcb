#!/usr/bin/env python3
"""
简化的核心图像处理器测试
验证基本功能
"""
import sys
import os
import numpy as np
import cv2
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_processor_imports():
    """测试处理器导入"""
    print("🧪 测试处理器导入...")
    
    try:
        from app.services.image_processors import (
            SharpenProcessor, EdgeDetectionProcessor, GammaCorrectionProcessor,
            validate_sharpen_parameters, validate_edge_detection_parameters, validate_gamma_correction_parameters
        )
        print("✅ 所有处理器导入成功")
        return True
        
    except Exception as e:
        print(f"❌ 处理器导入失败: {str(e)}")
        return False

def test_parameter_validation():
    """测试参数验证"""
    print("\n🧪 测试参数验证...")
    
    try:
        from app.services.image_processors import (
            validate_sharpen_parameters,
            validate_edge_detection_parameters,
            validate_gamma_correction_parameters
        )
        
        # 测试锐化参数
        sharpen_params = validate_sharpen_parameters({"intensity": 1.5})
        if sharpen_params["intensity"] == 1.5:
            print("✅ 锐化参数验证成功")
        else:
            print("❌ 锐化参数验证失败")
            return False
        
        # 测试边缘检测参数
        edge_params = validate_edge_detection_parameters({"low_threshold": 50, "high_threshold": 150})
        if edge_params["low_threshold"] == 50 and edge_params["high_threshold"] == 150:
            print("✅ 边缘检测参数验证成功")
        else:
            print("❌ 边缘检测参数验证失败")
            return False
        
        # 测试伽马校正参数
        gamma_params = validate_gamma_correction_parameters({"gamma": 1.2})
        if gamma_params["gamma"] == 1.2:
            print("✅ 伽马校正参数验证成功")
        else:
            print("❌ 伽马校正参数验证失败")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 参数验证测试失败: {str(e)}")
        return False

def test_image_processing_algorithms():
    """测试图像处理算法"""
    print("\n🧪 测试图像处理算法...")
    
    try:
        from app.services.image_processors import SharpenProcessor, EdgeDetectionProcessor, GammaCorrectionProcessor
        from app.services.minio_service import get_minio_service
        from app.core.database import get_db_session
        
        # 创建测试图像
        test_image = np.random.randint(0, 255, (50, 50, 3), dtype=np.uint8)
        
        # 获取服务（不需要实际连接来测试算法）
        db = get_db_session()
        minio_service = get_minio_service()
        
        try:
            # 测试锐化
            sharpen_processor = SharpenProcessor(minio_service, db)
            sharpen_result = sharpen_processor.process_image(test_image, {"intensity": 1.0})
            if sharpen_result.shape == test_image.shape:
                print("✅ 锐化算法处理成功")
            else:
                print("❌ 锐化算法处理失败")
                return False
            
            # 测试边缘检测
            edge_processor = EdgeDetectionProcessor(minio_service, db)
            edge_result = edge_processor.process_image(test_image, {"low_threshold": 50, "high_threshold": 150})
            if edge_result.shape == test_image.shape:
                print("✅ 边缘检测算法处理成功")
            else:
                print("❌ 边缘检测算法处理失败")
                return False
            
            # 测试伽马校正
            gamma_processor = GammaCorrectionProcessor(minio_service, db)
            gamma_result = gamma_processor.process_image(test_image, {"gamma": 1.2})
            if gamma_result.shape == test_image.shape:
                print("✅ 伽马校正算法处理成功")
            else:
                print("❌ 伽马校正算法处理失败")
                return False
            
            return True
            
        finally:
            db.close()
        
    except Exception as e:
        print(f"❌ 图像处理算法测试失败: {str(e)}")
        return False

def test_task_registration():
    """测试任务注册"""
    print("\n🧪 测试任务注册...")
    
    try:
        from app.tasks.image_tasks import (
            process_image_sharpen,
            process_image_edge_detection,
            process_image_gamma_correction
        )
        
        tasks = [
            ("锐化", process_image_sharpen),
            ("边缘检测", process_image_edge_detection),
            ("伽马校正", process_image_gamma_correction)
        ]
        
        for name, task in tasks:
            if hasattr(task, 'name'):
                print(f"✅ {name}任务注册成功: {task.name}")
            else:
                print(f"❌ {name}任务注册失败")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ 任务注册测试失败: {str(e)}")
        return False

def test_error_handling():
    """测试错误处理"""
    print("\n🧪 测试错误处理...")
    
    try:
        from app.services.image_processors import (
            validate_sharpen_parameters,
            validate_edge_detection_parameters,
            validate_gamma_correction_parameters
        )
        
        # 测试无效参数
        test_cases = [
            (validate_sharpen_parameters, {"intensity": -1}, "负强度"),
            (validate_edge_detection_parameters, {"low_threshold": 200, "high_threshold": 100}, "无效阈值"),
            (validate_gamma_correction_parameters, {"gamma": 0}, "零伽马")
        ]
        
        for validator, params, description in test_cases:
            try:
                validator(params)
                print(f"❌ {description}参数未被拒绝")
                return False
            except ValueError:
                print(f"✅ {description}参数正确被拒绝")
        
        return True
        
    except Exception as e:
        print(f"❌ 错误处理测试失败: {str(e)}")
        return False

if __name__ == "__main__":
    print("🚀 开始核心图像处理器简化测试...")
    
    success1 = test_processor_imports()
    success2 = test_parameter_validation()
    success3 = test_image_processing_algorithms()
    success4 = test_task_registration()
    success5 = test_error_handling()
    
    if success1 and success2 and success3 and success4 and success5:
        print("\n🎉 所有核心图像处理器测试通过！")
        print("\n📋 2.3.2核心任务验证:")
        print("✅ 处理器导入：所有核心处理器正确导入")
        print("✅ 参数验证：新旧格式参数转换和验证正常")
        print("✅ 算法实现：锐化、边缘检测、伽马校正算法正确")
        print("✅ 任务注册：所有Celery任务正确注册")
        print("✅ 错误处理：参数验证和异常处理完善")
        print("\n🎯 2.3.2核心图像处理任务迁移完成！")
        sys.exit(0)
    else:
        print("\n💥 核心图像处理器测试失败！")
        sys.exit(1)
