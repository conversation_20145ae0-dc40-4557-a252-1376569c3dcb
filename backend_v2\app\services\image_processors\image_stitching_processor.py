"""
图像拼接处理器
实现多图像全景拼接功能，支持PANORAMA和SCANS模式
"""
from typing import Dict, Any, List
import cv2
import numpy as np
import structlog
from sqlalchemy.orm import Session

from app.services.image_processor_base import ImageProcessorBase, ParameterConverter
from app.services.minio_service import MinIOService
from app.models.file import File

logger = structlog.get_logger()


class ImageStitchingProcessor(ImageProcessorBase):
    """图像拼接处理器 - 处理多文件输入"""
    
    def process_multiple_images(
        self, 
        file_objects: List[File], 
        parameters: Dict[str, Any],
        operation_name: str,
        task_id: str = None
    ) -> Dict[str, Any]:
        """
        处理多个图像的拼接
        
        Args:
            file_objects: 文件对象列表（至少2个文件）
            parameters: 处理参数
            operation_name: 操作名称
            task_id: 任务ID
            
        Returns:
            处理结果字典
        """
        import time
        start_time = time.time()
        
        try:
            if len(file_objects) < 2:
                raise ValueError(f"Image stitching requires at least 2 images, got {len(file_objects)}")
            
            # 更新进度：开始下载
            if task_id:
                self.update_task_progress(task_id, 10, 100, "Downloading images...")
            
            # 下载所有图像
            images = []
            for i, file_obj in enumerate(file_objects):
                image, temp_path = self.download_image_from_minio(file_obj)
                images.append(image)
                
                # 更新下载进度
                progress = 10 + (i + 1) * 20 // len(file_objects)
                if task_id:
                    self.update_task_progress(task_id, progress, 100, f"Downloaded image {i+1}/{len(file_objects)}")
            
            # 更新进度：开始拼接
            if task_id:
                self.update_task_progress(task_id, 40, 100, "Stitching images...")
            
            # 处理图像拼接
            stitched_image = self.process_image_stitching(images, parameters)
            
            # 更新进度：开始上传
            if task_id:
                self.update_task_progress(task_id, 80, 100, "Uploading result...")
            
            # 计算处理时间
            processing_time = time.time() - start_time
            
            # 上传结果（使用第一个文件作为基础信息）
            result = self.upload_processed_image(
                processed_image=stitched_image,
                original_file=file_objects[0],
                operation_name=operation_name,
                parameters=parameters,
                processing_time=processing_time
            )
            
            # 在元数据中添加拼接信息
            result["metadata"]["images_count"] = len(file_objects)
            result["metadata"]["source_file_ids"] = [f.id for f in file_objects]
            result["metadata"]["stitching_mode"] = parameters.get('mode', 'panorama')
            
            # 更新进度：完成
            if task_id:
                self.update_task_progress(task_id, 100, 100, "Stitching completed")
            
            logger.info("image_stitching_completed",
                       file_count=len(file_objects),
                       processing_time=processing_time)
            
            return result
            
        except Exception as e:
            logger.error("image_stitching_failed",
                        file_count=len(file_objects),
                        error=str(e))
            raise
        finally:
            # 确保清理临时文件
            self.cleanup_temp_files()
    
    def process_image_stitching(
        self, 
        images: List[np.ndarray], 
        parameters: Dict[str, Any]
    ) -> np.ndarray:
        """
        执行图像拼接算法
        
        Args:
            images: 图像列表
            parameters: 拼接参数
                - mode: 拼接模式（'panorama' 或 'scans'）
                
        Returns:
            拼接后的图像
        """
        try:
            mode = parameters.get('mode', 'panorama')
            
            logger.info("processing_image_stitching", 
                       image_count=len(images),
                       mode=mode,
                       image_shapes=[img.shape for img in images])
            
            # 创建拼接器
            if mode.lower() == 'scans':
                stitcher = cv2.Stitcher.create(cv2.Stitcher_SCANS)
            else:
                stitcher = cv2.Stitcher.create(cv2.Stitcher_PANORAMA)
            
            # 执行拼接
            status, stitched = stitcher.stitch(images)
            
            # 检查拼接状态
            if status != cv2.Stitcher_OK:
                error_messages = {
                    cv2.Stitcher_ERR_NEED_MORE_IMGS: "Need more images for stitching",
                    cv2.Stitcher_ERR_HOMOGRAPHY_EST_FAIL: "Homography estimation failed - images may not overlap enough",
                    cv2.Stitcher_ERR_CAMERA_PARAMS_ADJUST_FAIL: "Camera parameters adjustment failed"
                }
                error_msg = error_messages.get(status, f"Stitching failed with status {status}")
                raise ValueError(error_msg)
            
            logger.info("image_stitching_algorithm_completed",
                       output_shape=stitched.shape,
                       mode=mode,
                       input_count=len(images))
            
            return stitched
            
        except Exception as e:
            logger.error("image_stitching_algorithm_failed", 
                        mode=mode,
                        image_count=len(images),
                        error=str(e))
            raise ValueError(f"Image stitching algorithm failed: {str(e)}")
    
    def process_image(self, image: np.ndarray, parameters: Dict[str, Any]) -> np.ndarray:
        """
        单图像处理接口（图像拼接不适用）
        """
        raise NotImplementedError("Image stitching requires multiple images. Use process_multiple_images instead.")


def create_image_stitching_processor(minio_service: MinIOService, db_session: Session) -> ImageStitchingProcessor:
    """
    创建图像拼接处理器实例
    
    Args:
        minio_service: MinIO服务实例
        db_session: 数据库会话
        
    Returns:
        图像拼接处理器实例
    """
    return ImageStitchingProcessor(minio_service, db_session)


def validate_image_stitching_parameters(parameters: Dict[str, Any]) -> Dict[str, Any]:
    """
    验证和转换图像拼接处理参数
    
    Args:
        parameters: 输入参数
        
    Returns:
        验证后的参数
        
    Raises:
        ValueError: 参数无效时抛出
    """
    try:
        # 提取模式参数
        mode = parameters.get('mode', 'panorama')
        
        # 验证模式
        valid_modes = ['panorama', 'scans']
        if mode.lower() not in valid_modes:
            raise ValueError(f"Mode must be one of {valid_modes}, got '{mode}'")
        
        return {
            'mode': mode.lower()
        }
        
    except Exception as e:
        logger.error("image_stitching_parameter_validation_failed", 
                    parameters=parameters,
                    error=str(e))
        raise ValueError(f"Parameter validation failed: {str(e)}")


# 为了向后兼容，提供旧格式的函数接口
def image_stitching_legacy(file_paths: List[str], mode: str = 'panorama') -> str:
    """
    旧格式的图像拼接函数接口（向后兼容）
    
    Args:
        file_paths: 图像文件路径列表
        mode: 拼接模式
        
    Returns:
        处理后的图像路径
        
    Note:
        这个函数仅用于向后兼容，新代码应使用ImageStitchingProcessor
    """
    logger.warning("using_legacy_image_stitching_interface", 
                  file_paths=file_paths,
                  mode=mode)
    
    # 这里可以实现向旧接口的适配逻辑
    # 但在新架构中，建议直接使用ImageStitchingProcessor
    raise NotImplementedError("Legacy interface not implemented in new architecture. Use ImageStitchingProcessor instead.")
