#!/usr/bin/env python3
"""
测试直接文件上传功能
"""
import sys
import os
import io
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from fastapi.testclient import TestClient
from app.main import app
from app.core.database import get_db_session
from app.crud.crud_file import file as file_crud

def test_direct_upload():
    """测试直接文件上传"""
    print("🧪 测试直接文件上传...")
    
    try:
        # 创建测试客户端
        client = TestClient(app)
        
        # 获取数据库会话
        db = get_db_session()
        
        # 查询上传前的文件数量
        files_before = len(file_crud.get_multi(db=db, limit=1000))
        print(f"📊 上传前文件数量: {files_before}")
        
        # 创建测试文件
        test_file_content = b"This is a test image file content"
        test_file = io.BytesIO(test_file_content)
        
        # 准备上传数据
        files = {
            "file": ("test_image.jpg", test_file, "image/jpeg")
        }
        
        print("🔄 开始上传文件...")
        
        # 发送上传请求
        response = client.post("/api/files/upload", files=files)
        
        print(f"📡 响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 上传成功!")
            print(f"🆔 文件ID: {result.get('file_id')}")
            print(f"💾 数据库ID: {result.get('database_id')}")
            print(f"📁 文件名: {result.get('filename')}")
            print(f"📏 文件大小: {result.get('size')}")
            print(f"📂 存储路径: {result.get('object_name')}")
            print(f"📊 状态: {result.get('status')}")
            
            # 验证数据库记录
            files_after = len(file_crud.get_multi(db=db, limit=1000))
            print(f"📊 上传后文件数量: {files_after}")
            
            if files_after == files_before + 1:
                print("✅ 数据库记录创建成功！")
                
                # 查询具体的文件记录
                if result.get('database_id'):
                    db_file = file_crud.get(db=db, id=result['database_id'])
                    if db_file:
                        print(f"📝 数据库记录详情:")
                        print(f"   - ID: {db_file.id}")
                        print(f"   - 文件名: {db_file.filename}")
                        print(f"   - 文件类型: {db_file.file_type}")
                        print(f"   - 状态: {db_file.status}")
                        print(f"   - 存储路径: {db_file.storage_path}")
                        print(f"   - 文件大小: {db_file.file_size}")
                        print(f"   - 创建时间: {db_file.created_at}")
                        return True
                    else:
                        print("❌ 无法查询到数据库记录")
                        return False
                else:
                    print("⚠️ 响应中没有数据库ID")
                    return True  # 上传成功但没有数据库ID
            else:
                print("❌ 数据库记录数量不正确")
                return False
        else:
            print(f"❌ 上传失败: {response.status_code}")
            print(f"错误详情: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        if 'db' in locals():
            db.close()

if __name__ == "__main__":
    success = test_direct_upload()
    if success:
        print("\n🎉 直接上传测试通过！")
        sys.exit(0)
    else:
        print("\n💥 直接上传测试失败！")
        sys.exit(1)
