#!/usr/bin/env python3
"""
简化的图像处理基础测试
验证核心组件功能
"""
import sys
import os
import numpy as np
import cv2
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_opencv_basic():
    """测试OpenCV基础功能"""
    print("🧪 测试OpenCV基础功能...")
    
    try:
        # 创建测试图像
        test_image = np.random.randint(0, 255, (100, 100, 3), dtype=np.uint8)
        print(f"✅ 创建测试图像成功，形状: {test_image.shape}")
        
        # 测试灰度转换
        gray = cv2.cvtColor(test_image, cv2.COLOR_BGR2GRAY)
        gray_3channel = cv2.cvtColor(gray, cv2.COLOR_GRAY2BGR)
        print(f"✅ 灰度转换成功，输出形状: {gray_3channel.shape}")
        
        # 验证灰度图特性
        if np.array_equal(gray_3channel[:,:,0], gray_3channel[:,:,1]):
            print("✅ 灰度图验证通过（所有通道相同）")
        else:
            print("❌ 灰度图验证失败")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ OpenCV基础功能测试失败: {str(e)}")
        return False

def test_parameter_converter():
    """测试参数转换器"""
    print("\n🧪 测试参数转换器...")
    
    try:
        from app.services.image_processor_base import ParameterConverter
        
        # 测试锐化参数
        params = {"intensity": 2.0}
        converted = ParameterConverter.convert_legacy_parameters("sharpen", params)
        
        if converted.get("intensity") == 2.0:
            print("✅ 参数转换成功")
        else:
            print(f"❌ 参数转换失败: {converted}")
            return False
        
        # 测试参数验证
        is_valid, msg = ParameterConverter.validate_parameters("sharpen", {"intensity": 1.5})
        if is_valid:
            print("✅ 参数验证通过")
        else:
            print(f"❌ 参数验证失败: {msg}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 参数转换器测试失败: {str(e)}")
        return False

def test_grayscale_processor():
    """测试灰度处理器"""
    print("\n🧪 测试灰度处理器...")
    
    try:
        from app.services.image_processors.grayscale_processor import GrayscaleProcessor
        from app.services.minio_service import get_minio_service
        from app.core.database import get_db_session
        
        # 创建测试图像
        test_image = np.random.randint(0, 255, (50, 50, 3), dtype=np.uint8)
        
        # 创建处理器（不需要实际的MinIO和DB连接来测试process_image方法）
        db = get_db_session()
        minio_service = get_minio_service()
        
        try:
            processor = GrayscaleProcessor(minio_service, db)
            
            # 测试图像处理
            result = processor.process_image(test_image, {})
            
            if result.shape == (50, 50, 3):
                print("✅ 灰度处理器输出形状正确")
            else:
                print(f"❌ 输出形状错误: {result.shape}")
                return False
            
            # 验证灰度特性
            if np.array_equal(result[:,:,0], result[:,:,1]):
                print("✅ 灰度处理正确")
            else:
                print("❌ 灰度处理失败")
                return False
            
            return True
            
        finally:
            db.close()
        
    except Exception as e:
        print(f"❌ 灰度处理器测试失败: {str(e)}")
        return False

def test_task_import():
    """测试任务导入"""
    print("\n🧪 测试任务导入...")
    
    try:
        from app.tasks.image_tasks import process_image_grayscale
        
        if hasattr(process_image_grayscale, 'name'):
            print(f"✅ 任务导入成功: {process_image_grayscale.name}")
        else:
            print("❌ 任务导入失败")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 任务导入测试失败: {str(e)}")
        return False

if __name__ == "__main__":
    print("🚀 开始图像处理基础组件测试...")
    
    success1 = test_opencv_basic()
    success2 = test_parameter_converter()
    success3 = test_grayscale_processor()
    success4 = test_task_import()
    
    if success1 and success2 and success3 and success4:
        print("\n🎉 所有基础组件测试通过！")
        print("\n📋 2.3.1基础架构验证:")
        print("✅ OpenCV集成：图像处理库正常工作")
        print("✅ 参数转换器：新旧格式兼容性处理")
        print("✅ 灰度处理器：图像处理算法实现")
        print("✅ Celery任务：任务注册和导入正常")
        print("\n🚀 基础架构已就绪，可以进行下一步开发！")
        sys.exit(0)
    else:
        print("\n💥 基础组件测试失败！")
        sys.exit(1)
