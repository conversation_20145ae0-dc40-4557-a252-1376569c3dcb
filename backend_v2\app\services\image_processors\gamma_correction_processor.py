"""
伽马校正处理器
实现图像伽马校正功能，调整图像的亮度和对比度
"""
from typing import Dict, Any
import cv2
import numpy as np
import structlog

from app.services.image_processor_base import ImageProcessorBase, ParameterConverter

logger = structlog.get_logger()


class GammaCorrectionProcessor(ImageProcessorBase):
    """伽马校正处理器"""
    
    def process_image(self, image: np.ndarray, parameters: Dict[str, Any]) -> np.ndarray:
        """
        对图像进行伽马校正
        
        Args:
            image: 输入图像（BGR格式）
            parameters: 处理参数
                - gamma: 伽马值，范围0.1-3.0，默认1.0
                  - gamma < 1.0: 图像变亮
                  - gamma = 1.0: 无变化
                  - gamma > 1.0: 图像变暗
                
        Returns:
            伽马校正后的图像
        """
        try:
            gamma = parameters.get('gamma', 1.0)
            
            logger.info("processing_gamma_correction", 
                       input_shape=image.shape,
                       gamma=gamma)
            
            # 如果gamma接近1.0，直接返回原图像
            if abs(gamma - 1.0) < 0.01:
                logger.info("gamma_correction_skipped", reason="gamma_near_1.0")
                return image.copy()
            
            # 构建查找表（LUT）进行伽马校正
            # 公式：output = 255 * (input/255)^gamma
            inv_gamma = 1.0 / gamma
            table = np.array([
                ((i / 255.0) ** inv_gamma) * 255 
                for i in np.arange(0, 256)
            ]).astype(np.uint8)
            
            # 应用查找表
            corrected = cv2.LUT(image, table)
            
            logger.info("gamma_correction_completed",
                       output_shape=corrected.shape,
                       gamma=gamma,
                       brightness_change=np.mean(corrected) - np.mean(image))
            
            return corrected
            
        except Exception as e:
            logger.error("gamma_correction_failed", 
                        gamma=parameters.get('gamma'),
                        error=str(e))
            raise ValueError(f"Gamma correction failed: {str(e)}")


def create_gamma_correction_processor(minio_service, db_session) -> GammaCorrectionProcessor:
    """
    创建伽马校正处理器实例
    
    Args:
        minio_service: MinIO服务实例
        db_session: 数据库会话
        
    Returns:
        伽马校正处理器实例
    """
    return GammaCorrectionProcessor(minio_service, db_session)


def validate_gamma_correction_parameters(parameters: Dict[str, Any]) -> Dict[str, Any]:
    """
    验证和转换伽马校正处理参数
    
    Args:
        parameters: 输入参数
        
    Returns:
        验证后的参数
        
    Raises:
        ValueError: 参数无效时抛出
    """
    try:
        # 转换旧格式参数
        converted_params = ParameterConverter.convert_legacy_parameters("gamma_correction", parameters)
        
        # 验证参数
        is_valid, error_msg = ParameterConverter.validate_parameters("gamma_correction", converted_params)
        if not is_valid:
            raise ValueError(error_msg)
        
        # 确保gamma参数存在且在合理范围内
        gamma = converted_params.get('gamma', 1.0)
        
        # 类型转换和范围检查
        try:
            gamma = float(gamma)
        except (ValueError, TypeError):
            raise ValueError("Gamma must be a number")
        
        if gamma < 0.1 or gamma > 3.0:
            raise ValueError("Gamma must be between 0.1 and 3.0")
        
        return {
            'gamma': gamma
        }
        
    except Exception as e:
        logger.error("gamma_correction_parameter_validation_failed", 
                    parameters=parameters,
                    error=str(e))
        raise ValueError(f"Parameter validation failed: {str(e)}")


def calculate_optimal_gamma(image: np.ndarray, target_brightness: float = 128.0) -> float:
    """
    计算最优伽马值以达到目标亮度
    
    Args:
        image: 输入图像
        target_brightness: 目标平均亮度（0-255）
        
    Returns:
        建议的伽马值
    """
    try:
        # 计算当前平均亮度
        current_brightness = np.mean(image)
        
        if current_brightness <= 0:
            return 1.0
        
        # 估算所需的伽马值
        # 这是一个近似计算，实际效果可能需要微调
        gamma_estimate = np.log(target_brightness / 255.0) / np.log(current_brightness / 255.0)
        
        # 限制在合理范围内
        gamma_estimate = max(0.1, min(3.0, gamma_estimate))
        
        logger.info("optimal_gamma_calculated",
                   current_brightness=current_brightness,
                   target_brightness=target_brightness,
                   suggested_gamma=gamma_estimate)
        
        return gamma_estimate
        
    except Exception as e:
        logger.error("optimal_gamma_calculation_failed", error=str(e))
        return 1.0


# 为了向后兼容，提供旧格式的函数接口
def gamma_correction_legacy(image_path: str, gamma: float = 1.0) -> str:
    """
    旧格式的伽马校正函数接口（向后兼容）
    
    Args:
        image_path: 图像文件路径
        gamma: 伽马值
        
    Returns:
        处理后的图像路径
        
    Note:
        这个函数仅用于向后兼容，新代码应使用GammaCorrectionProcessor
    """
    logger.warning("using_legacy_gamma_correction_interface", 
                  image_path=image_path,
                  gamma=gamma)
    
    # 这里可以实现向旧接口的适配逻辑
    # 但在新架构中，建议直接使用GammaCorrectionProcessor
    raise NotImplementedError("Legacy interface not implemented in new architecture. Use GammaCorrectionProcessor instead.")
