"""
Celery应用配置
配置Celery与FastAPI的集成
"""

from celery import Celery
from app.core.config import settings
import structlog

logger = structlog.get_logger()

# 创建Celery应用实例
celery_app = Celery(
    "clover_tasks",
    broker=settings.CELERY_BROKER_URL,
    backend=settings.CELERY_RESULT_BACKEND,
    include=[
        "app.tasks.image_tasks",
        "app.tasks.video_tasks",
        "app.tasks.batch_tasks",
        "app.tasks.lifecycle_tasks"
    ]
)

# Celery配置
celery_app.conf.update(
    # 任务序列化
    task_serializer="json",
    accept_content=["json"],
    result_serializer="json",
    timezone="UTC",
    enable_utc=True,
    
    # 任务路由配置
    task_routes={
        # 图像处理任务路由
        "app.tasks.image_tasks.process_image_sharpen": {"queue": "cpu"},
        "app.tasks.image_tasks.process_image_grayscale": {"queue": "cpu"},
        "app.tasks.image_tasks.process_image_edge_detection": {"queue": "cpu"},
        "app.tasks.image_tasks.process_image_gamma_correction": {"queue": "cpu"},
        "app.tasks.image_tasks.process_image_fusion": {"queue": "gpu"},
        "app.tasks.image_tasks.process_image_stitching": {"queue": "gpu"},
        "app.tasks.image_tasks.process_beauty_enhancement": {"queue": "gpu"},
        "app.tasks.image_tasks.process_texture_transfer": {"queue": "gpu"},

        # 视频处理任务路由
        "app.tasks.video_tasks.process_video_resize": {"queue": "gpu"},
        "app.tasks.video_tasks.process_video_grayscale": {"queue": "cpu"},
        "app.tasks.video_tasks.process_video_extract_frame": {"queue": "io"},
        "app.tasks.video_tasks.process_video_edge_detection": {"queue": "gpu"},
        "app.tasks.video_tasks.process_video_blur": {"queue": "cpu"},
        "app.tasks.video_tasks.process_video_binary": {"queue": "cpu"},
        "app.tasks.video_tasks.process_video_transform": {"queue": "gpu"},
        "app.tasks.video_tasks.process_video_thumbnail": {"queue": "io"},

        # 批次处理任务路由
        "app.tasks.batch_tasks.process_batch": {"queue": "hybrid"},
        "app.tasks.batch_tasks.monitor_batch_progress": {"queue": "io"},

        # 文件生命周期管理任务路由
        "app.tasks.lifecycle_tasks.cleanup_expired_files": {"queue": "io"},
        "app.tasks.lifecycle_tasks.archive_old_files": {"queue": "io"},
        "app.tasks.lifecycle_tasks.generate_lifecycle_report": {"queue": "io"},
        "app.tasks.lifecycle_tasks.cleanup_temp_files": {"queue": "io"},
        "app.tasks.lifecycle_tasks.update_file_access_stats": {"queue": "io"},
    },
    
    # 队列配置
    task_default_queue="cpu",
    task_queues={
        "cpu": {
            "exchange": "cpu",
            "exchange_type": "direct",
            "routing_key": "cpu",
        },
        "gpu": {
            "exchange": "gpu", 
            "exchange_type": "direct",
            "routing_key": "gpu",
        },
        "io": {
            "exchange": "io",
            "exchange_type": "direct", 
            "routing_key": "io",
        },
        "hybrid": {
            "exchange": "hybrid",
            "exchange_type": "direct",
            "routing_key": "hybrid",
        },
    },
    
    # 工作进程配置
    worker_prefetch_multiplier=1,
    task_acks_late=True,
    worker_max_tasks_per_child=1000,
    
    # 任务执行配置
    task_soft_time_limit=300,  # 5分钟软限制
    task_time_limit=600,       # 10分钟硬限制
    task_track_started=True,
    task_send_sent_event=True,
    
    # 结果配置
    result_expires=3600,       # 结果保存1小时
    result_persistent=True,
    
    # 监控配置
    worker_send_task_events=True,
    
    # 重试配置
    task_default_retry_delay=60,
    task_max_retries=3,
    
    # 批次配置
    task_always_eager=False,   # 生产环境设为False
    task_eager_propagates=True,

    # 定时任务配置 (Celery Beat)
    beat_schedule={
        # 每日凌晨2点清理过期文件（试运行）
        'cleanup-expired-files-dry-run': {
            'task': 'app.tasks.lifecycle_tasks.cleanup_expired_files',
            'schedule': 60.0 * 60.0 * 24.0,  # 24小时
            'args': (True,),  # dry_run=True
        },

        # 每周日凌晨3点执行实际清理
        'cleanup-expired-files-real': {
            'task': 'app.tasks.lifecycle_tasks.cleanup_expired_files',
            'schedule': 60.0 * 60.0 * 24.0 * 7.0,  # 7天
            'args': (False,),  # dry_run=False
        },

        # 每月1号凌晨4点归档旧文件
        'archive-old-files': {
            'task': 'app.tasks.lifecycle_tasks.archive_old_files',
            'schedule': 60.0 * 60.0 * 24.0 * 30.0,  # 30天
            'args': (365,),  # archive_days=365
        },

        # 每小时清理临时文件
        'cleanup-temp-files': {
            'task': 'app.tasks.lifecycle_tasks.cleanup_temp_files',
            'schedule': 60.0 * 60.0,  # 1小时
        },

        # 每6小时生成生命周期报告
        'generate-lifecycle-report': {
            'task': 'app.tasks.lifecycle_tasks.generate_lifecycle_report',
            'schedule': 60.0 * 60.0 * 6.0,  # 6小时
        },

        # 每12小时更新文件访问统计
        'update-file-access-stats': {
            'task': 'app.tasks.lifecycle_tasks.update_file_access_stats',
            'schedule': 60.0 * 60.0 * 12.0,  # 12小时
        },
    },
    beat_schedule_filename='celerybeat-schedule',
)

# 任务状态回调配置
@celery_app.task(bind=True)
def task_success_callback(self, retval, task_id, args, kwargs):
    """任务成功回调"""
    logger.info(
        "celery_task_success",
        task_id=task_id,
        result=retval,
        args=args,
        kwargs=kwargs
    )

@celery_app.task(bind=True)
def task_failure_callback(self, task_id, error, traceback, args, kwargs):
    """任务失败回调"""
    logger.error(
        "celery_task_failure",
        task_id=task_id,
        error=str(error),
        traceback=traceback,
        args=args,
        kwargs=kwargs
    )

# 配置任务回调
celery_app.conf.task_annotations = {
    "*": {
        "on_success": task_success_callback,
        "on_failure": task_failure_callback,
    }
}

# Celery信号处理
from celery.signals import task_prerun, task_postrun, task_failure, task_success

@task_prerun.connect
def task_prerun_handler(sender=None, task_id=None, task=None, args=None, kwargs=None, **kwds):
    """任务开始前的处理"""
    logger.info(
        "celery_task_prerun",
        task_id=task_id,
        task_name=task.name if task else None,
        args=args,
        kwargs=kwargs
    )

@task_postrun.connect
def task_postrun_handler(sender=None, task_id=None, task=None, args=None, kwargs=None, retval=None, state=None, **kwds):
    """任务完成后的处理"""
    logger.info(
        "celery_task_postrun",
        task_id=task_id,
        task_name=task.name if task else None,
        state=state,
        retval=retval
    )

@task_success.connect
def task_success_handler(sender=None, task_id=None, result=None, **kwds):
    """任务成功处理"""
    logger.info(
        "celery_task_success_signal",
        task_id=task_id,
        result=result
    )

@task_failure.connect
def task_failure_handler(sender=None, task_id=None, exception=None, einfo=None, **kwds):
    """任务失败处理"""
    logger.error(
        "celery_task_failure_signal",
        task_id=task_id,
        exception=str(exception),
        traceback=str(einfo)
    )

# 健康检查任务
@celery_app.task
def health_check():
    """Celery健康检查任务"""
    return {"status": "healthy", "timestamp": "2025-08-06"}

logger.info("celery_app_configured", broker=settings.CELERY_BROKER_URL)
