"""
文件生命周期管理任务
实现文件清理、归档等定时任务
"""

from typing import Dict, Any
import structlog
from celery import current_task
from sqlalchemy.orm import Session

from app.core.celery_app import celery_app
from app.core.database import get_db_session
from app.services.file_lifecycle_service import FileLifecycleService
from app.services.minio_service import get_minio_service

logger = structlog.get_logger()


@celery_app.task(bind=True, name="app.tasks.lifecycle_tasks.cleanup_expired_files")
def cleanup_expired_files(self, dry_run: bool = True):
    """
    清理过期文件的定时任务
    
    Args:
        dry_run: 是否为试运行模式
    """
    logger.info("starting_file_cleanup", dry_run=dry_run, task_id=self.request.id)
    
    db = None
    try:
        # 获取数据库会话
        db = get_db_session()
        
        # 获取MinIO服务
        minio_service = get_minio_service()
        
        # 执行文件清理
        result = FileLifecycleService.cleanup_expired_files(
            db=db, 
            minio_service=minio_service, 
            dry_run=dry_run
        )
        
        # 记录清理结果
        logger.info(
            "file_cleanup_completed",
            task_id=self.request.id,
            total_found=result["total_found"],
            marked_for_deletion=result["marked_for_deletion"],
            storage_cleaned=result["storage_cleaned"],
            errors_count=len(result["errors"]),
            dry_run=dry_run
        )
        
        return result
        
    except Exception as e:
        error_msg = str(e)
        logger.error("file_cleanup_failed", task_id=self.request.id, error=error_msg)
        raise Exception(f"File cleanup failed: {error_msg}")
    finally:
        if db:
            db.close()


@celery_app.task(bind=True, name="app.tasks.lifecycle_tasks.archive_old_files")
def archive_old_files(self, archive_days: int = 365):
    """
    归档旧文件的定时任务
    
    Args:
        archive_days: 归档天数阈值
    """
    logger.info("starting_file_archival", archive_days=archive_days, task_id=self.request.id)
    
    db = None
    try:
        # 获取数据库会话
        db = get_db_session()
        
        # 获取MinIO服务
        minio_service = get_minio_service()
        
        # 执行文件归档
        result = FileLifecycleService.archive_old_files(
            db=db,
            minio_service=minio_service,
            archive_days=archive_days
        )
        
        # 记录归档结果
        logger.info(
            "file_archival_completed",
            task_id=self.request.id,
            total_candidates=result["total_candidates"],
            archived=result["archived"],
            errors_count=len(result["errors"]),
            archive_threshold=archive_days
        )
        
        return result
        
    except Exception as e:
        error_msg = str(e)
        logger.error("file_archival_failed", task_id=self.request.id, error=error_msg)
        raise Exception(f"File archival failed: {error_msg}")
    finally:
        if db:
            db.close()


@celery_app.task(bind=True, name="app.tasks.lifecycle_tasks.generate_lifecycle_report")
def generate_lifecycle_report(self):
    """
    生成文件生命周期统计报告的定时任务
    """
    logger.info("generating_lifecycle_report", task_id=self.request.id)
    
    db = None
    try:
        # 获取数据库会话
        db = get_db_session()
        
        # 生成统计报告
        stats = FileLifecycleService.get_lifecycle_statistics(db=db)
        
        # 记录报告生成结果
        logger.info(
            "lifecycle_report_generated",
            task_id=self.request.id,
            total_files=stats["total_files"],
            expired_files=stats["expiry_summary"]["expired"],
            expiring_soon=stats["expiry_summary"]["expiring_soon"],
            permanent_files=stats["expiry_summary"]["permanent"]
        )
        
        return stats
        
    except Exception as e:
        error_msg = str(e)
        logger.error("lifecycle_report_failed", task_id=self.request.id, error=error_msg)
        raise Exception(f"Lifecycle report generation failed: {error_msg}")
    finally:
        if db:
            db.close()


@celery_app.task(bind=True, name="app.tasks.lifecycle_tasks.cleanup_temp_files")
def cleanup_temp_files(self):
    """
    清理临时文件的定时任务（每小时执行）
    """
    logger.info("starting_temp_file_cleanup", task_id=self.request.id)
    
    db = None
    try:
        # 获取数据库会话
        db = get_db_session()
        
        # 获取MinIO服务
        minio_service = get_minio_service()
        
        # 查找临时文件
        from app.services.file_lifecycle_service import FileLifecyclePolicy
        temp_files = FileLifecycleService.find_files_by_policy(
            db=db, 
            policy=FileLifecyclePolicy.TEMP_FILES,
            limit=1000
        )
        
        cleaned_count = 0
        errors = []
        
        for temp_file in temp_files:
            try:
                if FileLifecycleService.is_file_expired(temp_file):
                    # 标记为删除
                    if FileLifecycleService.mark_file_for_deletion(db, temp_file, "temp_cleanup"):
                        # 从存储删除
                        if minio_service.delete_file(temp_file.storage_path):
                            cleaned_count += 1
                        else:
                            errors.append(f"Failed to delete from storage: {temp_file.id}")
                    else:
                        errors.append(f"Failed to mark for deletion: {temp_file.id}")
            except Exception as e:
                errors.append(f"Error processing temp file {temp_file.id}: {str(e)}")
        
        result = {
            "total_temp_files": len(temp_files),
            "cleaned_count": cleaned_count,
            "errors": errors
        }
        
        logger.info(
            "temp_file_cleanup_completed",
            task_id=self.request.id,
            total_temp_files=len(temp_files),
            cleaned_count=cleaned_count,
            errors_count=len(errors)
        )
        
        return result
        
    except Exception as e:
        error_msg = str(e)
        logger.error("temp_file_cleanup_failed", task_id=self.request.id, error=error_msg)
        raise Exception(f"Temp file cleanup failed: {error_msg}")
    finally:
        if db:
            db.close()


@celery_app.task(bind=True, name="app.tasks.lifecycle_tasks.update_file_access_stats")
def update_file_access_stats(self):
    """
    更新文件访问统计的定时任务
    """
    logger.info("updating_file_access_stats", task_id=self.request.id)
    
    db = None
    try:
        # 获取数据库会话
        db = get_db_session()
        
        # 这里可以实现文件访问统计的更新逻辑
        # 例如：统计下载次数、最后访问时间等
        
        # 目前返回基础统计信息
        stats = FileLifecycleService.get_lifecycle_statistics(db=db)
        
        # 可以在这里添加更多的访问统计逻辑
        # 例如：更新文件的last_accessed字段等
        
        logger.info(
            "file_access_stats_updated",
            task_id=self.request.id,
            total_files=stats["total_files"]
        )
        
        return {
            "updated": True,
            "total_files": stats["total_files"],
            "timestamp": stats["generated_at"]
        }
        
    except Exception as e:
        error_msg = str(e)
        logger.error("file_access_stats_update_failed", task_id=self.request.id, error=error_msg)
        raise Exception(f"File access stats update failed: {error_msg}")
    finally:
        if db:
            db.close()
