"""
视频处理基础服务
基于ImageProcessorBase扩展，提供统一的视频处理基础架构
支持流式处理、内存管理、FFmpeg集成等视频特有功能
"""
import os
import tempfile
import time
from typing import Dict, Any, Optional, Tuple, List, Iterator
from datetime import datetime, timedelta
import cv2
import numpy as np
from sqlalchemy.orm import Session
import structlog

from app.services.image_processor_base import ImageProcessorBase, ParameterConverter
from app.services.minio_service import MinIOService
from app.services.memory_monitor import MemoryMonitor, PerformanceConfig, create_memory_monitor, create_performance_config
from app.services.video_parameter_converter import VideoParameterConverter
from app.models.file import File, FileStatus, FileType
from app.schemas.file import FileCreate
from app.utils.storage_path import StoragePathManager, StorageType

logger = structlog.get_logger()


class VideoProcessorBase(ImageProcessorBase):
    """
    视频处理基础类
    继承ImageProcessorBase，扩展支持视频特有功能
    """
    
    def __init__(self, minio_service: MinIOService, db: Session):
        """
        初始化视频处理器

        Args:
            minio_service: MinIO服务实例
            db: 数据库会话
        """
        super().__init__(minio_service, db)

        # 视频特有属性
        self.video_capture = None
        self.video_writer = None
        self.video_info = {}
        self.frame_buffer = []
        self.current_frame_index = 0

        # 简化的内存监控和性能配置
        self.performance_config = create_performance_config()
        self.memory_monitor = create_memory_monitor(self.performance_config.memory_limit_mb)

        # 兼容性属性
        self.batch_size = self.performance_config.batch_size
        self.max_memory_mb = self.performance_config.memory_limit_mb
        
    def __exit__(self, exc_type, exc_val, exc_tb):
        """清理资源，包括视频对象和临时文件"""
        # 清理视频资源
        self.cleanup_video_resources()

        # 强制垃圾回收
        freed_mb = self.memory_monitor.force_garbage_collection()
        logger.info("video_processor_context_exited", freed_memory_mb=freed_mb)

        super().__exit__(exc_type, exc_val, exc_tb)
    
    def cleanup_video_resources(self):
        """清理视频相关资源"""
        try:
            if self.video_capture is not None:
                self.video_capture.release()
                self.video_capture = None
                logger.debug("video_capture_released")
            
            if self.video_writer is not None:
                self.video_writer.release()
                self.video_writer = None
                logger.debug("video_writer_released")
            
            # 清理帧缓冲
            self.frame_buffer.clear()
            self.current_frame_index = 0
            
        except Exception as e:
            logger.warning("video_resource_cleanup_failed", error=str(e))
    
    def download_video_from_minio(self, file_obj: File) -> Tuple[str, Dict[str, Any]]:
        """
        从MinIO下载视频文件并获取视频信息
        
        Args:
            file_obj: 文件对象
            
        Returns:
            (临时文件路径, 视频信息字典)
            
        Raises:
            ValueError: 文件下载或加载失败
        """
        try:
            logger.info("downloading_video_from_minio", 
                       file_id=file_obj.id, 
                       storage_path=file_obj.storage_path)
            
            # 从MinIO获取文件数据
            file_data = self.minio_service.get_file_data(str(file_obj.storage_path))
            if not file_data:
                raise ValueError(f"Failed to download file from MinIO: {file_obj.storage_path}")
            
            # 创建临时文件
            temp_fd, temp_path = tempfile.mkstemp(suffix=f"_{file_obj.filename}")
            self.temp_files.append(temp_path)
            
            try:
                # 写入临时文件
                with os.fdopen(temp_fd, 'wb') as temp_file:
                    temp_file.write(file_data)
                
                # 获取视频信息
                video_info = self.get_video_info(temp_path)
                
                logger.info("video_downloaded_successfully", 
                           file_id=file_obj.id,
                           video_info=video_info,
                           temp_path=temp_path)
                
                return temp_path, video_info
                
            except Exception as e:
                # 如果加载失败，清理临时文件
                try:
                    os.close(temp_fd)
                except:
                    pass
                raise e
                
        except Exception as e:
            logger.error("video_download_failed", 
                        file_id=file_obj.id,
                        error=str(e))
            raise ValueError(f"Failed to download and load video: {str(e)}")
    
    def get_video_info(self, video_path: str) -> Dict[str, Any]:
        """
        获取视频文件信息
        
        Args:
            video_path: 视频文件路径
            
        Returns:
            视频信息字典
        """
        try:
            cap = cv2.VideoCapture(video_path)
            if not cap.isOpened():
                raise ValueError(f"Cannot open video file: {video_path}")
            
            # 获取视频属性
            total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
            fps = cap.get(cv2.CAP_PROP_FPS)
            width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
            height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
            duration = total_frames / fps if fps > 0 else 0
            
            cap.release()
            
            video_info = {
                "total_frames": total_frames,
                "fps": fps,
                "width": width,
                "height": height,
                "duration": duration,
                "resolution": f"{width}x{height}"
            }
            
            logger.info("video_info_extracted", video_info=video_info)
            return video_info
            
        except Exception as e:
            logger.error("video_info_extraction_failed", 
                        video_path=video_path,
                        error=str(e))
            raise ValueError(f"Failed to extract video info: {str(e)}")
    
    def process_video_stream(
        self,
        video_path: str,
        parameters: Dict[str, Any],
        progress_callback: Optional[callable] = None
    ) -> Iterator[Tuple[int, np.ndarray]]:
        """
        流式处理视频帧（基础版本，保持兼容性）

        Args:
            video_path: 视频文件路径
            parameters: 处理参数
            progress_callback: 进度回调函数

        Yields:
            (帧索引, 处理后的帧)
        """
        cap = cv2.VideoCapture(video_path)
        if not cap.isOpened():
            raise ValueError(f"Cannot open video file: {video_path}")

        try:
            total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
            frame_index = 0

            while True:
                ret, frame = cap.read()
                if not ret:
                    break

                # 处理单帧（子类实现）
                processed_frame = self.process_frame(frame, parameters, frame_index)

                # 更新进度
                if progress_callback:
                    progress_callback(frame_index, total_frames)

                yield frame_index, processed_frame
                frame_index += 1

        finally:
            cap.release()

    def process_video_stream_with_batching(
        self,
        video_path: str,
        parameters: Dict[str, Any],
        progress_callback: Optional[callable] = None
    ) -> Iterator[List[Tuple[int, np.ndarray]]]:
        """
        简化的批处理流式处理视频帧
        基于后端设计文档的流式处理理念，使用固定批处理大小

        Args:
            video_path: 视频文件路径
            parameters: 处理参数
            progress_callback: 进度回调函数

        Yields:
            批次处理结果列表 [(帧索引, 处理后的帧), ...]
        """
        cap = cv2.VideoCapture(video_path)
        if not cap.isOpened():
            raise ValueError(f"Cannot open video file: {video_path}")

        try:
            total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
            frame_index = 0

            logger.info("video_stream_processing_started",
                       total_frames=total_frames,
                       batch_size=self.batch_size)

            while frame_index < total_frames:
                # 简单的内存检查
                if not self.memory_monitor.check_memory_limit():
                    freed_mb = self.memory_monitor.force_garbage_collection()
                    logger.warning("memory_limit_reached_during_processing",
                                 freed_mb=freed_mb, frame_index=frame_index)

                # 读取固定大小的批次帧
                batch_frames = []
                for _ in range(self.batch_size):
                    if frame_index >= total_frames:
                        break

                    ret, frame = cap.read()
                    if not ret:
                        break

                    batch_frames.append((frame_index, frame))
                    frame_index += 1

                if not batch_frames:
                    break

                # 批量处理帧
                processed_batch = []
                for idx, frame in batch_frames:
                    try:
                        processed_frame = self.process_frame(frame, parameters, idx)
                        processed_batch.append((idx, processed_frame))
                    except Exception as e:
                        logger.error("frame_processing_failed", frame_index=idx, error=str(e))
                        continue

                # 更新进度
                if progress_callback:
                    progress_callback(frame_index, total_frames)

                yield processed_batch

        finally:
            cap.release()
            logger.info("video_stream_processing_completed", total_frames=total_frames)
    
    def process_frame(
        self, 
        frame: np.ndarray, 
        parameters: Dict[str, Any], 
        frame_index: int
    ) -> np.ndarray:
        """
        处理单帧的抽象方法，子类需要实现
        
        Args:
            frame: 输入帧
            parameters: 处理参数
            frame_index: 帧索引
            
        Returns:
            处理后的帧
        """
        # 默认实现：调用图像处理方法
        return self.process_image(frame, parameters)

    def validate_and_convert_parameters(
        self,
        operation: str,
        parameters: Dict[str, Any]
    ) -> Tuple[bool, str, Dict[str, Any]]:
        """
        验证和转换视频处理参数

        Args:
            operation: 操作名称
            parameters: 原始参数

        Returns:
            (是否有效, 错误信息, 转换后的参数)
        """
        try:
            # 1. 转换旧格式参数
            converted_params = VideoParameterConverter.convert_legacy_parameters(operation, parameters)

            # 2. 验证参数有效性
            is_valid, error_msg = VideoParameterConverter.validate_parameters(operation, converted_params)
            if not is_valid:
                return False, error_msg, {}

            # 3. 合并默认参数
            default_params = VideoParameterConverter.get_default_parameters(operation)
            final_params = {**default_params, **converted_params}

            # 4. 集成性能配置
            final_params = VideoParameterConverter.merge_with_performance_config(
                final_params, self.performance_config
            )

            logger.info("parameters_validated_and_converted",
                       operation=operation,
                       original_params=parameters,
                       final_params=final_params)

            return True, "Parameters are valid", final_params

        except Exception as e:
            error_msg = f"Parameter validation and conversion failed: {str(e)}"
            logger.error("parameter_validation_error",
                        operation=operation,
                        parameters=parameters,
                        error=str(e))
            return False, error_msg, {}

    def extract_processing_parameters(
        self,
        parameters: Dict[str, Any]
    ) -> Tuple[Dict[str, Any], Dict[str, Any]]:
        """
        从参数中分离视频处理参数和性能参数

        Args:
            parameters: 完整参数字典

        Returns:
            (视频处理参数, 性能参数)
        """
        return VideoParameterConverter.extract_video_processing_parameters(parameters)

    def create_video_writer(
        self,
        output_path: str,
        fps: float,
        width: int,
        height: int,
        fourcc: str = 'mp4v'
    ) -> cv2.VideoWriter:
        """
        创建视频写入器

        Args:
            output_path: 输出视频路径
            fps: 帧率
            width: 视频宽度
            height: 视频高度
            fourcc: 视频编码格式

        Returns:
            VideoWriter对象
        """
        fourcc_code = cv2.VideoWriter_fourcc(*fourcc)
        writer = cv2.VideoWriter(output_path, fourcc_code, fps, (width, height))

        if not writer.isOpened():
            raise ValueError(f"Failed to create video writer for {output_path}")

        logger.info("video_writer_created",
                   output_path=output_path,
                   fps=fps, width=width, height=height,
                   fourcc=fourcc)

        return writer

    def write_frames_batch(
        self,
        writer: cv2.VideoWriter,
        frames_batch: List[Tuple[int, np.ndarray]]
    ):
        """
        批量写入视频帧

        Args:
            writer: VideoWriter对象
            frames_batch: 帧批次 [(帧索引, 帧数据), ...]
        """
        for frame_index, frame in frames_batch:
            try:
                writer.write(frame)
            except Exception as e:
                logger.error("frame_write_failed",
                           frame_index=frame_index,
                           error=str(e))
                raise

        logger.debug("frames_batch_written", batch_size=len(frames_batch))

    def process_video_with_streaming(
        self,
        input_path: str,
        output_path: str,
        parameters: Dict[str, Any],
        progress_callback: Optional[callable] = None
    ) -> Dict[str, Any]:
        """
        简化的流式视频处理流程
        基于后端设计文档的"流式解码 → 边处理边上传"理念

        Args:
            input_path: 输入视频路径
            output_path: 输出视频路径
            parameters: 处理参数
            progress_callback: 进度回调函数

        Returns:
            处理结果信息
        """
        start_time = time.time()

        # 获取输入视频信息
        input_info = self.get_video_info(input_path)

        # 创建视频写入器
        writer = self.create_video_writer(
            output_path,
            input_info['fps'],
            input_info['width'],
            input_info['height']
        )

        try:
            total_frames_processed = 0
            total_batches = 0

            # 简化的流式处理
            for batch_results in self.process_video_stream_with_batching(
                input_path, parameters, progress_callback
            ):
                # 批量写入处理结果
                self.write_frames_batch(writer, batch_results)

                total_frames_processed += len(batch_results)
                total_batches += 1

            processing_time = time.time() - start_time

            # 处理结果
            result = {
                'total_frames_processed': total_frames_processed,
                'total_batches': total_batches,
                'processing_time_seconds': processing_time,
                'average_fps': total_frames_processed / processing_time if processing_time > 0 else 0,
                'batch_size': self.batch_size,
                'memory_usage': self.memory_monitor.get_memory_usage().__dict__
            }

            logger.info("streaming_video_processing_completed", **result)

            return result

        finally:
            writer.release()
    
    def process_image(self, image: np.ndarray, parameters: Dict[str, Any]) -> np.ndarray:
        """
        处理图像的抽象方法，子类需要实现
        继承自ImageProcessorBase，保持接口一致性
        """
        raise NotImplementedError("Subclasses must implement process_image method")


class VideoParameterConverter(ParameterConverter):
    """
    视频参数转换器
    扩展ParameterConverter，支持视频特有参数
    """
    
    @staticmethod
    def convert_legacy_video_parameters(operation: str, legacy_params: Dict[str, Any]) -> Dict[str, Any]:
        """
        将旧格式视频参数转换为新格式
        
        Args:
            operation: 操作名称
            legacy_params: 旧格式参数
            
        Returns:
            新格式参数
        """
        # 先调用基础转换
        converted_params = ParameterConverter.convert_legacy_parameters(operation, legacy_params)
        
        # 处理视频特有参数
        if operation == "video_resize":
            # 确保width和height参数存在
            if "width" not in converted_params:
                converted_params["width"] = 640
            if "height" not in converted_params:
                converted_params["height"] = 480
            
            # 类型转换
            converted_params["width"] = int(converted_params["width"])
            converted_params["height"] = int(converted_params["height"])
        
        elif operation == "video_grayscale":
            # 视频灰度转换可能有特殊参数
            pass
        
        return converted_params
    
    @staticmethod
    def validate_video_parameters(operation: str, parameters: Dict[str, Any]) -> Tuple[bool, str]:
        """
        验证视频处理参数
        
        Args:
            operation: 操作名称
            parameters: 参数字典
            
        Returns:
            (是否有效, 错误信息)
        """
        # 先调用基础验证
        is_valid, error_msg = ParameterConverter.validate_parameters(operation, parameters)
        if not is_valid:
            return is_valid, error_msg
        
        # 视频特有验证
        if operation == "video_resize":
            width = parameters.get("width", 640)
            height = parameters.get("height", 480)
            
            if not isinstance(width, int) or width <= 0:
                return False, "Width must be a positive integer"
            
            if not isinstance(height, int) or height <= 0:
                return False, "Height must be a positive integer"
            
            if width > 4096 or height > 4096:
                return False, "Resolution too high (max 4096x4096)"
        
        return True, ""

    def upload_processed_video(
        self,
        processed_video_path: str,
        original_file: File,
        operation_name: str,
        parameters: Dict[str, Any],
        processing_time: float,
        video_info: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        上传处理后的视频到MinIO并创建数据库记录

        Args:
            processed_video_path: 处理后的视频文件路径
            original_file: 原始文件对象
            operation_name: 操作名称
            parameters: 处理参数
            processing_time: 处理耗时
            video_info: 视频信息

        Returns:
            包含结果信息的字典
        """
        try:
            logger.info("uploading_processed_video",
                       original_file_id=original_file.id,
                       operation=operation_name)

            # 生成输出文件名
            base_name = os.path.splitext(original_file.filename)[0]
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_filename = f"{operation_name}_{base_name}_{timestamp}.mp4"

            # 生成存储路径
            storage_path = StoragePathManager.generate_path(
                storage_type=StorageType.RESULT,
                filename=output_filename,
                task_id=getattr(original_file, 'task_id', None),
                batch_id=getattr(original_file, 'batch_id', None)
            )

            # 读取处理后的视频文件
            with open(processed_video_path, 'rb') as f:
                file_data = f.read()

            # 上传到MinIO
            upload_success = self.minio_service.upload_file_data(
                object_name=storage_path,
                file_data=file_data,
                content_type="video/mp4"
            )

            if not upload_success:
                raise ValueError("Failed to upload processed video to MinIO")

            # 计算文件大小和校验和
            file_size = len(file_data)
            from app.services.file_metadata_service import FileMetadataService
            checksum = FileMetadataService.calculate_checksum(file_data)

            # 创建数据库记录
            file_create = FileCreate(
                filename=output_filename,
                original_filename=output_filename,
                file_type=FileType.VIDEO,
                content_type="video/mp4",
                storage_path=storage_path,
                storage_bucket=self.minio_service.bucket_name,
                file_size=file_size,
                checksum=checksum,
                file_metadata={
                    "operation": operation_name,
                    "parameters": parameters,
                    "processing_time": processing_time,
                    "original_file_id": original_file.id,
                    "video_info": video_info,
                    "created_at": datetime.now().isoformat()
                },
                batch_id=getattr(original_file, 'batch_id', None),
                upload_session_id=None
            )

            # 保存到数据库
            from app.crud import file as file_crud
            result_file = file_crud.create(db=self.db, obj_in=file_create)

            # 更新文件状态为已上传
            result_file.status = FileStatus.UPLOADED
            self.db.commit()

            # 生成预签名下载URL
            download_url = self.minio_service.generate_presigned_get_url(
                object_name=storage_path,
                expires=timedelta(hours=24)
            )

            result = {
                "success": True,
                "output_file_id": result_file.id,
                "output_filename": output_filename,
                "output_path": storage_path,
                "minio_url": download_url,
                "file_size": file_size,
                "checksum": checksum,
                "processing_time": processing_time,
                "metadata": {
                    "operation": operation_name,
                    "parameters": parameters,
                    "original_file_id": original_file.id,
                    "video_info": video_info
                }
            }

            logger.info("processed_video_uploaded_successfully",
                       original_file_id=original_file.id,
                       result_file_id=result_file.id,
                       output_filename=output_filename)

            return result

        except Exception as e:
            logger.error("processed_video_upload_failed",
                        original_file_id=original_file.id,
                        operation=operation_name,
                        error=str(e))
            raise ValueError(f"Failed to upload processed video: {str(e)}")

    def execute_video_processing(
        self,
        file_obj: File,
        parameters: Dict[str, Any],
        operation_name: str,
        task_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        执行完整的视频处理流程

        Args:
            file_obj: 输入文件对象
            parameters: 处理参数
            operation_name: 操作名称
            task_id: 任务ID（可选）

        Returns:
            处理结果字典
        """
        start_time = time.time()

        try:
            # 更新进度：开始下载
            if task_id:
                self.update_task_progress(task_id, 5, 100, "Downloading video...")

            # 下载视频
            video_path, video_info = self.download_video_from_minio(file_obj)

            # 更新进度：开始处理
            if task_id:
                self.update_task_progress(task_id, 15, 100, f"Processing video with {operation_name}...")

            # 处理视频（子类实现具体逻辑）
            processed_video_path = self.process_video(video_path, parameters, video_info, task_id)

            # 更新进度：开始上传
            if task_id:
                self.update_task_progress(task_id, 85, 100, "Uploading result...")

            # 计算处理时间
            processing_time = time.time() - start_time

            # 上传结果
            result = self.upload_processed_video(
                processed_video_path=processed_video_path,
                original_file=file_obj,
                operation_name=operation_name,
                parameters=parameters,
                processing_time=processing_time,
                video_info=video_info
            )

            # 更新进度：完成
            if task_id:
                self.update_task_progress(task_id, 100, 100, "Video processing completed")

            logger.info("video_processing_completed",
                       file_id=file_obj.id,
                       operation=operation_name,
                       processing_time=processing_time)

            return result

        except Exception as e:
            logger.error("video_processing_failed",
                        file_id=file_obj.id,
                        operation=operation_name,
                        error=str(e))
            raise
        finally:
            # 确保清理临时文件和视频资源
            self.cleanup_video_resources()
            self.cleanup_temp_files()

    def process_video(
        self,
        video_path: str,
        parameters: Dict[str, Any],
        video_info: Dict[str, Any],
        task_id: Optional[str] = None
    ) -> str:
        """
        处理视频的抽象方法，子类需要实现

        Args:
            video_path: 输入视频路径
            parameters: 处理参数
            video_info: 视频信息
            task_id: 任务ID

        Returns:
            处理后的视频文件路径
        """
        raise NotImplementedError("Subclasses must implement process_video method")
