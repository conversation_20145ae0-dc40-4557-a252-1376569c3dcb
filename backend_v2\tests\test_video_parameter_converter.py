#!/usr/bin/env python3
"""
测试视频参数转换器功能
验证VideoParameterConverter的参数验证、转换和集成功能
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_parameter_conversion():
    """测试参数转换功能"""
    print("\n🧪 测试参数转换功能...")
    
    try:
        from app.services.video_parameter_converter import VideoParameterConverter
        
        # 测试分辨率预设转换
        legacy_params = {
            "resolution": "1080p",
            "codec": "h264",
            "quality": "medium"
        }
        
        converted = VideoParameterConverter.convert_legacy_parameters(
            "video_format_conversion", legacy_params
        )
        
        print(f"✅ 分辨率预设转换: {legacy_params['resolution']} -> {converted.get('width')}x{converted.get('height')}")
        print(f"✅ 编码格式转换: {legacy_params['codec']} -> {converted.get('codec')}")
        print(f"✅ 质量预设转换: {legacy_params['quality']} -> CRF {converted.get('crf')}")
        
        # 验证转换结果
        if converted.get('width') == 1920 and converted.get('height') == 1080:
            print("✅ 1080p分辨率转换正确")
        else:
            print("❌ 1080p分辨率转换失败")
            return False
        
        if converted.get('codec') == 'libx264':
            print("✅ H264编码格式转换正确")
        else:
            print("❌ H264编码格式转换失败")
            return False
        
        if converted.get('crf') == 23:
            print("✅ 中等质量转换正确")
        else:
            print("❌ 中等质量转换失败")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 参数转换测试失败: {str(e)}")
        return False

def test_parameter_validation():
    """测试参数验证功能"""
    print("\n🧪 测试参数验证功能...")
    
    try:
        from app.services.video_parameter_converter import VideoParameterConverter
        
        # 测试有效参数
        valid_params = {
            "width": 1280,
            "height": 720,
            "fps": 30.0,
            "codec": "libx264",
            "crf": 23,
            "preset": "fast",
            "pixel_format": "yuv420p"
        }
        
        is_valid, error_msg = VideoParameterConverter.validate_parameters(
            "video_format_conversion", valid_params
        )
        
        if is_valid:
            print("✅ 有效参数验证通过")
        else:
            print(f"❌ 有效参数验证失败: {error_msg}")
            return False
        
        # 测试无效参数
        invalid_test_cases = [
            # 无效分辨率
            ({"width": 10, "height": 720}, "Width must be an integer between 32 and 7680"),
            ({"width": 1280, "height": 10000}, "Height must be an integer between 32 and 4320"),
            
            # 无效帧率
            ({"fps": 0.5}, "FPS must be between 1.0 and 120.0"),
            ({"fps": 200}, "FPS must be between 1.0 and 120.0"),
            
            # 无效编码格式
            ({"codec": "invalid_codec"}, "Codec must be one of:"),
            
            # 无效CRF值
            ({"crf": -1}, "CRF must be an integer between 0 and 51"),
            ({"crf": 100}, "CRF must be an integer between 0 and 51"),
            
            # 无效预设
            ({"preset": "invalid_preset"}, "Preset must be one of:"),
            
            # 无效像素格式
            ({"pixel_format": "invalid_format"}, "Pixel format must be one of:")
        ]
        
        for invalid_params, expected_error_part in invalid_test_cases:
            is_valid, error_msg = VideoParameterConverter.validate_parameters(
                "video_format_conversion", invalid_params
            )
            
            if not is_valid and expected_error_part in error_msg:
                print(f"✅ 无效参数正确拒绝: {list(invalid_params.keys())[0]}")
            else:
                print(f"❌ 无效参数验证失败: {invalid_params} -> {error_msg}")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ 参数验证测试失败: {str(e)}")
        return False

def test_default_parameters():
    """测试默认参数功能"""
    print("\n🧪 测试默认参数功能...")
    
    try:
        from app.services.video_parameter_converter import VideoParameterConverter
        
        # 测试各种操作的默认参数
        operations = ["video_grayscale", "video_resize", "video_format_conversion"]
        
        for operation in operations:
            defaults = VideoParameterConverter.get_default_parameters(operation)
            print(f"✅ {operation}默认参数: {defaults}")
        
        # 验证video_resize的默认参数
        resize_defaults = VideoParameterConverter.get_default_parameters("video_resize")
        if resize_defaults.get("width") == 1280 and resize_defaults.get("height") == 720:
            print("✅ video_resize默认分辨率正确")
        else:
            print("❌ video_resize默认分辨率错误")
            return False
        
        # 验证video_format_conversion的默认参数
        format_defaults = VideoParameterConverter.get_default_parameters("video_format_conversion")
        if (format_defaults.get("codec") == "libx264" and 
            format_defaults.get("crf") == 23 and
            format_defaults.get("preset") == "fast"):
            print("✅ video_format_conversion默认参数正确")
        else:
            print("❌ video_format_conversion默认参数错误")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 默认参数测试失败: {str(e)}")
        return False

def test_performance_config_integration():
    """测试性能配置集成"""
    print("\n🧪 测试性能配置集成...")
    
    try:
        from app.services.video_parameter_converter import VideoParameterConverter
        from app.services.memory_monitor import PerformanceConfig
        
        # 创建性能配置
        performance_config = PerformanceConfig(
            batch_size=10,
            memory_limit_mb=2048,
            progress_update_interval=5
        )
        
        # 测试参数合并
        original_params = {
            "width": 1280,
            "height": 720,
            "codec": "libx264"
        }
        
        merged_params = VideoParameterConverter.merge_with_performance_config(
            original_params, performance_config
        )
        
        # 验证合并结果
        if (merged_params.get("batch_size") == 10 and
            merged_params.get("memory_limit_mb") == 2048 and
            merged_params.get("progress_update_interval") == 5):
            print("✅ 性能配置合并成功")
        else:
            print("❌ 性能配置合并失败")
            return False
        
        # 验证原始参数保留
        if (merged_params.get("width") == 1280 and
            merged_params.get("height") == 720 and
            merged_params.get("codec") == "libx264"):
            print("✅ 原始参数保留正确")
        else:
            print("❌ 原始参数保留失败")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 性能配置集成测试失败: {str(e)}")
        return False

def test_parameter_extraction():
    """测试参数分离功能"""
    print("\n🧪 测试参数分离功能...")
    
    try:
        from app.services.video_parameter_converter import VideoParameterConverter
        
        # 测试参数分离
        mixed_params = {
            "width": 1280,
            "height": 720,
            "codec": "libx264",
            "batch_size": 15,
            "memory_limit_mb": 4096,
            "progress_update_interval": 10,
            "fps": 30.0
        }
        
        video_params, performance_params = VideoParameterConverter.extract_video_processing_parameters(
            mixed_params
        )
        
        # 验证视频参数
        expected_video_keys = {"width", "height", "codec", "fps"}
        if set(video_params.keys()) == expected_video_keys:
            print("✅ 视频参数分离正确")
        else:
            print(f"❌ 视频参数分离失败: {set(video_params.keys())} != {expected_video_keys}")
            return False
        
        # 验证性能参数
        expected_performance_keys = {"batch_size", "memory_limit_mb", "progress_update_interval"}
        if set(performance_params.keys()) == expected_performance_keys:
            print("✅ 性能参数分离正确")
        else:
            print(f"❌ 性能参数分离失败: {set(performance_params.keys())} != {expected_performance_keys}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 参数分离测试失败: {str(e)}")
        return False

def test_video_processor_integration():
    """测试VideoProcessorBase集成"""
    print("\n🧪 测试VideoProcessorBase集成...")
    
    try:
        from app.services.memory_monitor import create_memory_monitor, create_performance_config
        
        # 模拟VideoProcessorBase的参数验证
        class MockVideoProcessor:
            def __init__(self):
                self.performance_config = create_performance_config()
                self.memory_monitor = create_memory_monitor()
            
            def validate_and_convert_parameters(self, operation, parameters):
                from app.services.video_parameter_converter import VideoParameterConverter
                
                # 转换参数
                converted_params = VideoParameterConverter.convert_legacy_parameters(operation, parameters)
                
                # 验证参数
                is_valid, error_msg = VideoParameterConverter.validate_parameters(operation, converted_params)
                if not is_valid:
                    return False, error_msg, {}
                
                # 合并默认参数
                default_params = VideoParameterConverter.get_default_parameters(operation)
                final_params = {**default_params, **converted_params}
                
                # 集成性能配置
                final_params = VideoParameterConverter.merge_with_performance_config(
                    final_params, self.performance_config
                )
                
                return True, "Parameters are valid", final_params
        
        # 测试集成
        processor = MockVideoProcessor()
        
        # 测试参数验证和转换
        test_params = {
            "resolution": "720p",
            "codec": "h264",
            "quality": "high"
        }
        
        is_valid, error_msg, final_params = processor.validate_and_convert_parameters(
            "video_format_conversion", test_params
        )
        
        if is_valid:
            print("✅ VideoProcessorBase集成参数验证成功")
            print(f"   - 最终参数包含: {list(final_params.keys())}")
        else:
            print(f"❌ VideoProcessorBase集成参数验证失败: {error_msg}")
            return False
        
        # 验证关键参数
        if (final_params.get("width") == 1280 and
            final_params.get("height") == 720 and
            final_params.get("codec") == "libx264" and
            final_params.get("crf") == 18 and  # high quality
            final_params.get("batch_size") == 15):  # from performance config
            print("✅ 集成参数转换和合并正确")
        else:
            print("❌ 集成参数转换和合并失败")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ VideoProcessorBase集成测试失败: {str(e)}")
        return False

if __name__ == "__main__":
    print("🚀 开始视频参数转换器测试...")
    
    success1 = test_parameter_conversion()
    success2 = test_parameter_validation()
    success3 = test_default_parameters()
    success4 = test_performance_config_integration()
    success5 = test_parameter_extraction()
    success6 = test_video_processor_integration()
    
    if success1 and success2 and success3 and success4 and success5 and success6:
        print("\n🎉 所有视频参数转换器测试通过！")
        print("\n📋 2.4.4验证完成:")
        print("✅ 参数转换：分辨率预设、编码格式、质量预设转换正常")
        print("✅ 参数验证：分辨率、帧率、编码格式、质量参数验证正确")
        print("✅ 默认参数：各种操作的默认参数配置合理")
        print("✅ 性能配置集成：PerformanceConfig正确合并到参数中")
        print("✅ 参数分离：视频参数和性能参数正确分离")
        print("✅ VideoProcessorBase集成：完整的参数验证和转换流程")
        print("\n🎯 2.4.4: 参数验证和转换系统建立完成！")
        sys.exit(0)
    else:
        print("\n💥 视频参数转换器测试失败！")
        sys.exit(1)
