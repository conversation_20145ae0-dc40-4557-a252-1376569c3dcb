#!/usr/bin/env python3
"""
测试文件下载302重定向功能
"""
import sys
import os
import io
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from fastapi.testclient import TestClient
from app.main import app
from app.core.database import get_db_session
from app.crud.crud_file import file as file_crud

def test_download_redirect():
    """测试文件下载302重定向"""
    print("🧪 测试文件下载302重定向...")
    
    try:
        # 创建测试客户端
        client = TestClient(app)
        
        # 获取数据库会话
        db = get_db_session()
        
        # 首先上传一个测试文件（使用允许的图片格式）
        test_file_content = b"This is a test image file for download testing"
        test_file = io.BytesIO(test_file_content)

        files = {
            "file": ("test_download.jpg", test_file, "image/jpeg")
        }
        
        print("🔄 先上传测试文件...")
        upload_response = client.post("/api/files/upload", files=files)
        
        if upload_response.status_code != 200:
            print(f"❌ 上传失败: {upload_response.status_code}")
            print(f"错误详情: {upload_response.text}")
            return False
        
        upload_result = upload_response.json()
        file_id = upload_result.get('file_id')
        database_id = upload_result.get('database_id')
        filename = upload_result.get('filename')
        
        print(f"✅ 文件上传成功!")
        print(f"🆔 文件ID: {file_id}")
        print(f"💾 数据库ID: {database_id}")
        print(f"📁 文件名: {filename}")
        
        # 测试通过数据库ID下载（推荐方式）
        print(f"\n🔄 测试通过数据库ID下载: {database_id}")
        download_response = client.get(f"/api/files/{database_id}/download", follow_redirects=False)
        
        print(f"📡 下载响应状态码: {download_response.status_code}")
        
        if download_response.status_code == 302:
            redirect_url = download_response.headers.get('location')
            print(f"✅ 302重定向成功!")
            print(f"🔗 重定向URL: {redirect_url[:100]}...")
            
            # 验证重定向URL是否有效（包含MinIO预签名URL特征）
            if 'X-Amz-Algorithm' in redirect_url and 'X-Amz-Signature' in redirect_url:
                print("✅ 预签名URL格式正确!")

                # 对于测试，我们只验证重定向是否正确工作
                # 实际的MinIO下载需要真实的MinIO服务器
                print("✅ 302重定向机制工作正常!")
                return True
            elif redirect_url.startswith('http://localhost:9001/'):
                # MinIO本地服务器URL格式
                print("✅ MinIO本地服务器URL格式正确!")

                # 尝试使用requests直接访问MinIO URL
                try:
                    import requests
                    response = requests.get(redirect_url, timeout=5)
                    if response.status_code == 200:
                        if response.content == test_file_content:
                            print("✅ 文件内容下载正确!")
                            return True
                        else:
                            print(f"❌ 文件内容不匹配")
                            return False
                    else:
                        print(f"⚠️ MinIO直接访问失败: {response.status_code}")
                        print("✅ 但302重定向机制工作正常!")
                        return True
                except Exception as e:
                    print(f"⚠️ MinIO直接访问异常: {str(e)}")
                    print("✅ 但302重定向机制工作正常!")
                    return True
            else:
                print("❌ 重定向URL格式不正确")
                return False
        else:
            print(f"❌ 期望302重定向，但得到: {download_response.status_code}")
            print(f"响应内容: {download_response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        if 'db' in locals():
            db.close()

def test_download_with_filename():
    """测试通过文件ID和文件名下载"""
    print("\n🧪 测试通过文件ID和文件名下载...")
    
    try:
        client = TestClient(app)
        
        # 使用一个不存在的文件ID测试传统方式
        file_id = "test-file-id"
        filename = "test.txt"
        
        print(f"🔄 测试下载: {file_id}/{filename}")
        download_response = client.get(f"/api/files/{file_id}/download?filename={filename}", follow_redirects=False)
        
        print(f"📡 响应状态码: {download_response.status_code}")
        
        # 这应该返回404，因为文件不存在
        if download_response.status_code == 404:
            print("✅ 正确返回404（文件不存在）")
            return True
        else:
            print(f"❌ 期望404，但得到: {download_response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        return False

if __name__ == "__main__":
    success1 = test_download_redirect()
    success2 = test_download_with_filename()
    
    if success1 and success2:
        print("\n🎉 所有下载重定向测试通过！")
        sys.exit(0)
    else:
        print("\n💥 下载重定向测试失败！")
        sys.exit(1)
