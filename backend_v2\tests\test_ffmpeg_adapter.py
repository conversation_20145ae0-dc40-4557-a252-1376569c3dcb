#!/usr/bin/env python3
"""
测试FFmpeg适配器
验证FFmpegAdapter的基础功能，包括硬件加速检测、错误诊断、音频提取等
基于专家评估建议的测试策略
"""
import sys
import os
import numpy as np
import cv2
import tempfile
import json
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_ffmpeg_adapter_imports():
    """测试FFmpeg适配器导入"""
    print("🧪 测试FFmpeg适配器导入...")
    
    try:
        from app.services.ffmpeg_adapter import FFmpegAdapter
        print("✅ FFmpegAdapter导入成功")
        return True
        
    except Exception as e:
        print(f"❌ FFmpegAdapter导入失败: {str(e)}")
        return False

def test_ffmpeg_availability():
    """测试FFmpeg可用性检查"""
    print("\n🧪 测试FFmpeg可用性检查...")
    
    try:
        from app.services.ffmpeg_adapter import FFmpegAdapter
        
        # 创建适配器实例（会自动检查FFmpeg可用性）
        adapter = FFmpegAdapter()
        print("✅ FFmpeg和FFprobe可用性检查通过")
        
        # 检查路径设置
        if adapter.ffmpeg_path and adapter.ffprobe_path:
            print("✅ FFmpeg路径配置正确")
        else:
            print("❌ FFmpeg路径配置错误")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ FFmpeg可用性检查失败: {str(e)}")
        return False

def test_hardware_acceleration_detection():
    """测试硬件加速检测"""
    print("\n🧪 测试硬件加速检测...")
    
    try:
        from app.services.ffmpeg_adapter import FFmpegAdapter
        
        adapter = FFmpegAdapter()
        
        # 检查硬件加速检测结果
        if adapter.hardware_accel:
            print(f"✅ 检测到硬件加速: {adapter.hardware_accel}")
        else:
            print("✅ 未检测到硬件加速，使用软件编码")
        
        # 测试环境变量覆盖
        os.environ['FFMPEG_HARDWARE_ACCEL'] = 'test_override'
        adapter_override = FFmpegAdapter()
        if adapter_override.hardware_accel == 'test_override':
            print("✅ 环境变量覆盖功能正常")
        else:
            print("❌ 环境变量覆盖功能异常")
            return False
        
        # 清理环境变量
        del os.environ['FFMPEG_HARDWARE_ACCEL']
        
        return True
        
    except Exception as e:
        print(f"❌ 硬件加速检测测试失败: {str(e)}")
        return False

def test_stderr_capture():
    """测试stderr捕获机制"""
    print("\n🧪 测试stderr捕获机制...")
    
    try:
        from app.services.ffmpeg_adapter import FFmpegAdapter
        
        adapter = FFmpegAdapter()
        
        # 测试正常命令
        return_code, stdout, stderr = adapter._capture_ffmpeg_stderr([adapter.ffmpeg_path, '-version'])
        if return_code == 0:
            print("✅ 正常命令执行和stderr捕获成功")
        else:
            print("❌ 正常命令执行失败")
            return False
        
        # 测试错误命令
        return_code, stdout, stderr = adapter._capture_ffmpeg_stderr([adapter.ffmpeg_path, '-invalid_option'])
        if return_code != 0 and stderr:
            print("✅ 错误命令stderr捕获成功")
        else:
            print("❌ 错误命令stderr捕获失败")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ stderr捕获测试失败: {str(e)}")
        return False

def test_video_info_extraction():
    """测试视频信息提取"""
    print("\n🧪 测试视频信息提取...")
    
    try:
        from app.services.ffmpeg_adapter import FFmpegAdapter
        
        # 创建一个简单的测试视频
        temp_fd, test_video_path = tempfile.mkstemp(suffix=".mp4")
        os.close(temp_fd)
        
        try:
            # 创建一个简单的测试视频（10帧，640x480）
            fourcc = cv2.VideoWriter_fourcc(*'mp4v')
            out = cv2.VideoWriter(test_video_path, fourcc, 30.0, (640, 480))
            
            # 写入10帧测试图像
            for i in range(10):
                frame = np.full((480, 640, 3), i * 25, dtype=np.uint8)
                out.write(frame)
            
            out.release()
            
            # 测试视频信息提取
            adapter = FFmpegAdapter()
            video_info = adapter.get_video_info(test_video_path)
            
            # 验证基本信息
            if video_info["width"] == 640 and video_info["height"] == 480:
                print("✅ 视频分辨率提取正确")
            else:
                print(f"❌ 视频分辨率错误: {video_info['width']}x{video_info['height']}")
                return False
            
            if video_info["fps"] == 30.0:
                print("✅ 视频帧率提取正确")
            else:
                print(f"❌ 视频帧率错误: {video_info['fps']}")
                return False
            
            # 验证专家建议的时间戳信息
            if 'start_time' in video_info and 'time_base' in video_info:
                print("✅ 时间戳信息提取成功（专家建议功能）")
            else:
                print("❌ 时间戳信息提取失败")
                return False
            
            return True
        
        finally:
            # 清理测试文件
            if os.path.exists(test_video_path):
                os.remove(test_video_path)
        
    except Exception as e:
        print(f"❌ 视频信息提取测试失败: {str(e)}")
        return False

def test_audio_extraction():
    """测试音频提取功能"""
    print("\n🧪 测试音频提取功能...")
    
    try:
        from app.services.ffmpeg_adapter import FFmpegAdapter
        
        # 创建一个简单的测试视频（无音频）
        temp_fd, test_video_path = tempfile.mkstemp(suffix=".mp4")
        os.close(temp_fd)
        
        try:
            # 创建无音频的测试视频
            fourcc = cv2.VideoWriter_fourcc(*'mp4v')
            out = cv2.VideoWriter(test_video_path, fourcc, 30.0, (640, 480))
            
            for i in range(5):
                frame = np.full((480, 640, 3), i * 50, dtype=np.uint8)
                out.write(frame)
            
            out.release()
            
            # 测试音频提取
            adapter = FFmpegAdapter()
            audio_path = adapter.extract_audio(test_video_path)
            
            # 无音频视频应该返回None
            if audio_path is None:
                print("✅ 无音频视频正确返回None")
            else:
                print("❌ 无音频视频错误返回音频路径")
                return False
            
            return True
        
        finally:
            # 清理测试文件
            if os.path.exists(test_video_path):
                os.remove(test_video_path)
        
    except Exception as e:
        print(f"❌ 音频提取测试失败: {str(e)}")
        return False

def test_resource_cleanup():
    """测试资源清理"""
    print("\n🧪 测试资源清理...")
    
    try:
        from app.services.ffmpeg_adapter import FFmpegAdapter
        
        # 使用上下文管理器
        with FFmpegAdapter() as adapter:
            # 添加一些临时文件
            temp_fd, temp_file = tempfile.mkstemp()
            os.close(temp_fd)
            adapter.temp_files.append(temp_file)
            
            # 确保文件存在
            if os.path.exists(temp_file):
                print("✅ 临时文件创建成功")
            else:
                print("❌ 临时文件创建失败")
                return False
        
        # 退出上下文后，临时文件应该被清理
        if not os.path.exists(temp_file):
            print("✅ 临时文件自动清理成功")
        else:
            print("❌ 临时文件清理失败")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 资源清理测试失败: {str(e)}")
        return False

if __name__ == "__main__":
    print("🚀 开始FFmpeg适配器测试...")
    
    success1 = test_ffmpeg_adapter_imports()
    success2 = test_ffmpeg_availability()
    success3 = test_hardware_acceleration_detection()
    success4 = test_stderr_capture()
    success5 = test_video_info_extraction()
    success6 = test_audio_extraction()
    success7 = test_resource_cleanup()
    
    if success1 and success2 and success3 and success4 and success5 and success6 and success7:
        print("\n🎉 所有FFmpeg适配器测试通过！")
        print("\n📋 Phase 1验证完成:")
        print("✅ FFmpeg适配器导入：FFmpegAdapter正确导入")
        print("✅ FFmpeg可用性：FFmpeg和FFprobe可用性检查通过")
        print("✅ 硬件加速检测：正确检测硬件加速，支持环境变量覆盖")
        print("✅ stderr捕获：完整的错误诊断机制（专家强调功能）")
        print("✅ 视频信息提取：正确提取分辨率、帧率、时间戳信息")
        print("✅ 音频提取：正确处理无音频视频的情况")
        print("✅ 资源清理：临时文件自动清理机制正常")
        print("\n🎯 Phase 1: FFmpegAdapter基础类建立完成！")
        sys.exit(0)
    else:
        print("\n💥 FFmpeg适配器测试失败！")
        sys.exit(1)
