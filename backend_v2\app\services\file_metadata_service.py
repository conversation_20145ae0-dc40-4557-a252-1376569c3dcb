"""
文件元数据管理服务
负责文件元数据的提取、管理和版本控制
"""
import hashlib
import os
from typing import Dict, Any, Optional, List, Tuple
from datetime import datetime
from sqlalchemy.orm import Session

from app.models.file import File, FileType, FileStatus
from app.crud.crud_file import file as file_crud
from app.schemas.file import FileCreate, FileUpdate
from app.utils.storage_path import StoragePathManager, StorageType


class FileMetadataService:
    """文件元数据管理服务"""
    
    @staticmethod
    def calculate_file_checksum(file_data: bytes, algorithm: str = "sha256") -> str:
        """
        计算文件校验和
        
        Args:
            file_data: 文件数据
            algorithm: 哈希算法（sha256, md5等）
            
        Returns:
            文件校验和
        """
        hash_obj = hashlib.new(algorithm)
        hash_obj.update(file_data)
        return hash_obj.hexdigest()
    
    @staticmethod
    def extract_image_metadata(file_path: str) -> Dict[str, Any]:
        """
        提取图像文件元数据
        
        Args:
            file_path: 文件路径
            
        Returns:
            图像元数据字典
        """
        metadata = {
            "type": "image",
            "extracted_at": datetime.now().isoformat()
        }
        
        try:
            from PIL import Image
            with Image.open(file_path) as img:
                metadata.update({
                    "width": img.width,
                    "height": img.height,
                    "format": img.format,
                    "mode": img.mode,
                    "has_transparency": img.mode in ('RGBA', 'LA') or 'transparency' in img.info
                })
                
                # 提取EXIF信息
                if hasattr(img, '_getexif') and img._getexif():
                    exif = img._getexif()
                    if exif:
                        metadata["exif"] = {k: v for k, v in exif.items() if isinstance(v, (str, int, float))}
                        
        except Exception as e:
            metadata["extraction_error"] = str(e)
            
        return metadata
    
    @staticmethod
    def extract_video_metadata(file_path: str) -> Dict[str, Any]:
        """
        提取视频文件元数据
        
        Args:
            file_path: 文件路径
            
        Returns:
            视频元数据字典
        """
        metadata = {
            "type": "video",
            "extracted_at": datetime.now().isoformat()
        }
        
        try:
            import cv2
            cap = cv2.VideoCapture(file_path)
            
            if cap.isOpened():
                metadata.update({
                    "width": int(cap.get(cv2.CAP_PROP_FRAME_WIDTH)),
                    "height": int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT)),
                    "fps": cap.get(cv2.CAP_PROP_FPS),
                    "frame_count": int(cap.get(cv2.CAP_PROP_FRAME_COUNT)),
                    "duration": cap.get(cv2.CAP_PROP_FRAME_COUNT) / cap.get(cv2.CAP_PROP_FPS) if cap.get(cv2.CAP_PROP_FPS) > 0 else 0
                })
                
            cap.release()
            
        except Exception as e:
            metadata["extraction_error"] = str(e)
            
        return metadata
    
    @classmethod
    def extract_file_metadata(cls, file_path: str, file_type: FileType) -> Dict[str, Any]:
        """
        根据文件类型提取元数据
        
        Args:
            file_path: 文件路径
            file_type: 文件类型
            
        Returns:
            文件元数据字典
        """
        base_metadata = {
            "file_size": os.path.getsize(file_path) if os.path.exists(file_path) else 0,
            "last_modified": datetime.fromtimestamp(os.path.getmtime(file_path)).isoformat() if os.path.exists(file_path) else None,
            "extraction_timestamp": datetime.now().isoformat()
        }
        
        if file_type == FileType.IMAGE:
            specific_metadata = cls.extract_image_metadata(file_path)
        elif file_type == FileType.VIDEO:
            specific_metadata = cls.extract_video_metadata(file_path)
        else:
            specific_metadata = {"type": file_type.value}
        
        base_metadata.update(specific_metadata)
        return base_metadata
    
    @staticmethod
    def check_file_duplication(
        db: Session,
        filename: str,
        file_size: int,
        checksum: Optional[str] = None
    ) -> Tuple[bool, List[File]]:
        """
        检查文件是否重复
        
        Args:
            db: 数据库会话
            filename: 文件名
            file_size: 文件大小
            checksum: 文件校验和（可选）
            
        Returns:
            (是否重复, 重复文件列表)
        """
        duplicates = []
        
        # 首先通过校验和检查（最准确）
        if checksum:
            duplicates = file_crud.find_duplicates_by_checksum(db=db, checksum=checksum)
            if duplicates:
                return True, duplicates
        
        # 通过文件名和大小检查（可能重复）
        possible_duplicates = file_crud.find_duplicates_by_name_and_size(
            db=db, filename=filename, file_size=file_size
        )
        
        return len(possible_duplicates) > 0, possible_duplicates
    
    @staticmethod
    def create_file_version(
        db: Session,
        original_filename: str,
        new_file_data: FileCreate,
        batch_id: Optional[int] = None
    ) -> File:
        """
        创建文件新版本
        
        Args:
            db: 数据库会话
            original_filename: 原始文件名
            new_file_data: 新文件数据
            batch_id: 批次ID
            
        Returns:
            新创建的文件对象
        """
        # 获取现有版本
        existing_versions = file_crud.get_file_versions(
            db=db, original_filename=original_filename, batch_id=batch_id
        )
        
        # 在元数据中记录版本信息
        metadata = new_file_data.file_metadata or {}
        metadata.update({
            "version_number": len(existing_versions) + 1,
            "is_latest_version": True,
            "previous_versions": [v.id for v in existing_versions],
            "version_created_at": datetime.now().isoformat()
        })
        
        # 更新旧版本的元数据，标记为非最新版本
        for old_version in existing_versions:
            old_metadata = old_version.file_metadata or {}
            old_metadata["is_latest_version"] = False
            file_crud.update_metadata(db=db, file_id=old_version.id, metadata=old_metadata)
        
        new_file_data.file_metadata = metadata
        return file_crud.create(db=db, obj_in=new_file_data)
    
    @staticmethod
    def organize_file_path(
        filename: str,
        storage_type: StorageType = StorageType.UPLOAD,
        task_id: Optional[str] = None,
        batch_id: Optional[int] = None
    ) -> Tuple[str, str]:
        """
        智能组织文件路径
        
        Args:
            filename: 文件名
            storage_type: 存储类型
            task_id: 任务ID（可选）
            batch_id: 批次ID（可选）
            
        Returns:
            (object_name, file_id) 元组
        """
        # 使用现有的路径管理器生成基础路径
        object_name, file_id = StoragePathManager.generate_file_path(
            filename=filename,
            storage_type=storage_type
        )
        
        # 如果有任务ID或批次ID，可以进一步组织路径
        if task_id or batch_id:
            path_parts = object_name.split('/')
            
            # 在文件ID前插入任务/批次信息
            if len(path_parts) >= 2:
                insert_index = -2  # 在文件ID目录前插入
                
                if task_id:
                    path_parts.insert(insert_index, f"task_{task_id}")
                    insert_index += 1
                
                if batch_id:
                    path_parts.insert(insert_index, f"batch_{batch_id}")
                
                object_name = "/".join(path_parts)
        
        return object_name, file_id
    
    @staticmethod
    def get_file_statistics(db: Session, file_type: Optional[FileType] = None) -> Dict[str, Any]:
        """
        获取文件统计信息
        
        Args:
            db: 数据库会话
            file_type: 文件类型（可选）
            
        Returns:
            统计信息字典
        """
        from sqlalchemy import func
        
        query = db.query(File)
        if file_type:
            query = query.filter(File.file_type == file_type)
        
        # 基础统计
        total_files = query.count()
        total_size = query.with_entities(func.sum(File.file_size)).scalar() or 0
        
        # 按状态统计
        status_stats = {}
        for status in FileStatus:
            count = query.filter(File.status == status).count()
            status_stats[status.value] = count
        
        # 按类型统计（如果没有指定类型）
        type_stats = {}
        if not file_type:
            for ftype in FileType:
                count = db.query(File).filter(File.file_type == ftype).count()
                type_stats[ftype.value] = count
        
        return {
            "total_files": total_files,
            "total_size_bytes": total_size,
            "total_size_mb": round(total_size / (1024 * 1024), 2) if total_size else 0,
            "status_distribution": status_stats,
            "type_distribution": type_stats,
            "generated_at": datetime.now().isoformat()
        }
