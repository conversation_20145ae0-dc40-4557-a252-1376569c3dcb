"""
任务持久化服务
提供统一的任务数据持久化和业务逻辑接口
"""

from typing import List, Optional, Dict, Any, Tuple
from sqlalchemy.orm import Session
from sqlalchemy.exc import SQLAlchemyError
import structlog
from datetime import datetime

from app.models.task import Task, TaskStatus
from app.models.batch import Batch, BatchStatus
from app.models.file import File, FileType, FileStatus
from app.schemas.task import TaskCreate, TaskUpdate
from app.schemas.batch import BatchCreate, BatchUpdate
from app.schemas.file import FileCreate, FileUpdate
from app.crud.crud_task import task as crud_task
from app.crud.crud_batch import batch as crud_batch
from app.crud.crud_file import file as crud_file
from app.services.task_management import TaskManagementService
from app.services.task_state_transition import StateTransitionError

logger = structlog.get_logger()


class TaskPersistenceError(Exception):
    """任务持久化错误"""
    pass


class TaskPersistenceService:
    """任务持久化服务"""
    
    @staticmethod
    def create_complete_task(
        db: Session,
        task_data: TaskCreate,
        batches_data: List[BatchCreate],
        files_data: List[FileCreate]
    ) -> Tuple[Task, List[Batch], List[File]]:
        """
        创建完整的任务（包含批次和文件）
        
        Args:
            db: 数据库会话
            task_data: 任务创建数据
            batches_data: 批次创建数据列表
            files_data: 文件创建数据列表
            
        Returns:
            (创建的任务, 创建的批次列表, 创建的文件列表)
            
        Raises:
            TaskPersistenceError: 创建失败时
        """
        try:
            # 开始事务
            logger.info("creating_complete_task", task_name=task_data.name)
            
            # 1. 创建任务
            created_task = crud_task.create(db=db, obj_in=task_data)
            logger.debug("task_created", task_id=created_task.id)
            
            # 2. 创建批次
            created_batches = []
            for batch_data in batches_data:
                batch_data.task_id = created_task.id
                created_batch = crud_batch.create(db=db, obj_in=batch_data)
                created_batches.append(created_batch)
                logger.debug("batch_created", batch_id=created_batch.id, task_id=created_task.id)
            
            # 3. 创建文件
            created_files = []
            for file_data in files_data:
                # 如果batch_id是索引，则替换为真实的批次ID
                if isinstance(file_data.batch_id, int) and file_data.batch_id < len(created_batches):
                    file_data.batch_id = created_batches[file_data.batch_id].id
                elif file_data.batch_id:
                    # 验证批次ID是否有效
                    valid_batch_ids = [b.id for b in created_batches]
                    if file_data.batch_id not in valid_batch_ids:
                        raise TaskPersistenceError(f"无效的批次ID: {file_data.batch_id}")

                created_file = crud_file.create(db=db, obj_in=file_data)
                created_files.append(created_file)
                logger.debug("file_created", file_id=created_file.id, batch_id=file_data.batch_id)
            
            # 4. 更新统计信息
            TaskPersistenceService._update_task_statistics(db, created_task, created_batches, created_files)
            
            # 5. 提交事务
            db.commit()
            
            logger.info(
                "complete_task_created_successfully",
                task_id=created_task.id,
                batches_count=len(created_batches),
                files_count=len(created_files)
            )
            
            return created_task, created_batches, created_files
            
        except Exception as e:
            # 回滚事务
            db.rollback()
            logger.error("create_complete_task_failed", error=str(e))
            raise TaskPersistenceError(f"创建任务失败: {str(e)}")
    
    @staticmethod
    def _update_task_statistics(
        db: Session,
        task: Task,
        batches: List[Batch],
        files: List[File]
    ):
        """更新任务统计信息"""
        # 更新任务的批次统计
        task.total_batches = len(batches)
        
        # 更新每个批次的文件统计
        for batch in batches:
            batch_files = [f for f in files if f.batch_id == batch.id]
            batch.total_files = len(batch_files)
            db.add(batch)
        
        db.add(task)
    
    @staticmethod
    def get_task_with_details(
        db: Session,
        task_id: int,
        include_files: bool = True
    ) -> Optional[Dict[str, Any]]:
        """
        获取任务详细信息
        
        Args:
            db: 数据库会话
            task_id: 任务ID
            include_files: 是否包含文件信息
            
        Returns:
            任务详细信息字典
        """
        try:
            # 获取任务
            task = crud_task.get(db=db, id=task_id)
            if not task:
                return None
            
            # 获取批次
            batches = crud_batch.get_by_task_id(db=db, task_id=task_id)
            
            # 构建响应数据
            task_details = {
                "task": {
                    "id": task.id,
                    "name": task.name,
                    "description": task.description,
                    "status": task.status.value,
                    "progress": task.progress,
                    "config": task.config,
                    "total_batches": task.total_batches,
                    "completed_batches": task.completed_batches,
                    "failed_batches": task.failed_batches,
                    "error_message": task.error_message,
                    "created_at": task.created_at.isoformat(),
                    "updated_at": task.updated_at.isoformat()
                },
                "batches": [],
                "statistics": {
                    "total_files": 0,
                    "processed_files": 0,
                    "failed_files": 0,
                    "total_size": 0
                }
            }
            
            # 处理批次信息
            for batch in batches:
                batch_info = {
                    "id": batch.id,
                    "name": batch.name,
                    "status": batch.status.value,
                    "progress": batch.progress,
                    "operation_type": batch.operation_type,
                    "queue_name": batch.queue_name,
                    "total_files": batch.total_files,
                    "processed_files": batch.processed_files,
                    "files": []
                }
                
                if include_files:
                    # 获取批次的文件
                    files = crud_file.get_by_batch_id(db=db, batch_id=batch.id)
                    for file in files:
                        file_info = {
                            "id": file.id,
                            "filename": file.filename,
                            "file_type": file.file_type.value,
                            "file_size": file.file_size,
                            "status": file.status.value,
                            "storage_path": file.storage_path
                        }
                        batch_info["files"].append(file_info)
                        
                        # 更新统计信息
                        task_details["statistics"]["total_files"] += 1
                        if file.status == FileStatus.PROCESSED:
                            task_details["statistics"]["processed_files"] += 1
                        elif file.status == FileStatus.FAILED:
                            task_details["statistics"]["failed_files"] += 1
                        
                        if file.file_size:
                            task_details["statistics"]["total_size"] += file.file_size
                
                task_details["batches"].append(batch_info)
            
            return task_details
            
        except Exception as e:
            logger.error("get_task_with_details_failed", task_id=task_id, error=str(e))
            raise TaskPersistenceError(f"获取任务详情失败: {str(e)}")
    
    @staticmethod
    def update_task_progress(
        db: Session,
        task_id: int
    ) -> Optional[Task]:
        """
        更新任务进度（基于批次和文件状态）
        
        Args:
            db: 数据库会话
            task_id: 任务ID
            
        Returns:
            更新后的任务对象
        """
        try:
            task = crud_task.get(db=db, id=task_id)
            if not task:
                return None
            
            # 获取所有批次
            batches = crud_batch.get_by_task_id(db=db, task_id=task_id)
            
            total_files = 0
            processed_files = 0
            completed_batches = 0
            failed_batches = 0
            
            for batch in batches:
                total_files += batch.total_files
                processed_files += batch.processed_files
                
                if batch.status == BatchStatus.SUCCESS:
                    completed_batches += 1
                elif batch.status == BatchStatus.FAILED:
                    failed_batches += 1
            
            # 计算进度
            if total_files > 0:
                progress = (processed_files / total_files) * 100
            else:
                progress = 0.0
            
            # 更新任务
            update_data = {
                "progress": progress,
                "completed_batches": completed_batches,
                "failed_batches": failed_batches
            }
            
            # 根据批次状态更新任务状态
            if completed_batches == len(batches) and len(batches) > 0:
                if failed_batches == 0:
                    update_data["status"] = TaskStatus.SUCCESS
                elif failed_batches == len(batches):
                    update_data["status"] = TaskStatus.FAILED
                else:
                    update_data["status"] = TaskStatus.PARTIAL
            
            updated_task = crud_task.update(db=db, db_obj=task, obj_in=update_data)
            db.commit()
            
            logger.info(
                "task_progress_updated",
                task_id=task_id,
                progress=progress,
                completed_batches=completed_batches,
                failed_batches=failed_batches
            )
            
            return updated_task
            
        except Exception as e:
            db.rollback()
            logger.error("update_task_progress_failed", task_id=task_id, error=str(e))
            raise TaskPersistenceError(f"更新任务进度失败: {str(e)}")
    
    @staticmethod
    def delete_task_cascade(
        db: Session,
        task_id: int
    ) -> bool:
        """
        级联删除任务（包含所有批次和文件）
        
        Args:
            db: 数据库会话
            task_id: 任务ID
            
        Returns:
            是否删除成功
        """
        try:
            task = crud_task.get(db=db, id=task_id)
            if not task:
                return False
            
            # 检查任务状态
            if not TaskManagementService.transition_task_status.__wrapped__(
                db, task_id, TaskStatus.CANCELLED
            ):
                logger.warning("cannot_delete_active_task", task_id=task_id, status=task.status.value)
                raise TaskPersistenceError("无法删除活跃状态的任务，请先取消任务")
            
            # 删除任务（级联删除批次和文件）
            crud_task.remove(db=db, id=task_id)
            db.commit()
            
            logger.info("task_deleted_cascade", task_id=task_id)
            return True
            
        except Exception as e:
            db.rollback()
            logger.error("delete_task_cascade_failed", task_id=task_id, error=str(e))
            raise TaskPersistenceError(f"删除任务失败: {str(e)}")
    
    @staticmethod
    def get_task_statistics(
        db: Session,
        task_id: Optional[int] = None
    ) -> Dict[str, Any]:
        """
        获取任务统计信息
        
        Args:
            db: 数据库会话
            task_id: 任务ID（可选，为None时返回全局统计）
            
        Returns:
            统计信息字典
        """
        try:
            if task_id:
                # 单个任务统计
                task_details = TaskPersistenceService.get_task_with_details(db, task_id)
                if not task_details:
                    return {}
                return task_details["statistics"]
            else:
                # 全局统计
                tasks = crud_task.get_multi(db=db, limit=1000)  # 限制查询数量
                
                stats = {
                    "total_tasks": len(tasks),
                    "pending_tasks": 0,
                    "in_progress_tasks": 0,
                    "completed_tasks": 0,
                    "failed_tasks": 0,
                    "cancelled_tasks": 0,
                    "total_files": 0,
                    "total_size": 0
                }
                
                for task in tasks:
                    # 统计任务状态
                    if task.status == TaskStatus.PENDING:
                        stats["pending_tasks"] += 1
                    elif task.status == TaskStatus.IN_PROGRESS:
                        stats["in_progress_tasks"] += 1
                    elif task.status == TaskStatus.SUCCESS:
                        stats["completed_tasks"] += 1
                    elif task.status == TaskStatus.FAILED:
                        stats["failed_tasks"] += 1
                    elif task.status == TaskStatus.CANCELLED:
                        stats["cancelled_tasks"] += 1
                
                return stats
                
        except Exception as e:
            logger.error("get_task_statistics_failed", task_id=task_id, error=str(e))
            raise TaskPersistenceError(f"获取统计信息失败: {str(e)}")
