"""
应用配置管理
"""
from typing import List, Optional
from pydantic import validator
from pydantic_settings import BaseSettings
import os


class Settings(BaseSettings):
    """应用配置类"""
    
    # 应用基础配置
    APP_NAME: str = "Clover Backend V2"
    APP_VERSION: str = "2.0.0"
    ENVIRONMENT: str = "development"
    DEBUG: bool = True
    SECRET_KEY: str = "your-secret-key-here-change-in-production"
    
    # 服务器配置
    HOST: str = "0.0.0.0"
    PORT: int = 8000
    
    # 数据库配置
    DATABASE_URL: str = "postgresql://clover:clover_password@localhost:5433/clover_v2"
    DATABASE_ECHO: bool = False
    
    # Redis配置
    REDIS_URL: str = "redis://localhost:6380/0"

    # Celery配置
    CELERY_BROKER_URL: str = "redis://localhost:6380/1"
    CELERY_RESULT_BACKEND: str = "redis://localhost:6380/2"
    CELERY_TASK_ALWAYS_EAGER: bool = False  # 开发时可设为True进行同步测试
    
    # MinIO配置
    MINIO_ENDPOINT: str = "localhost:9001"
    MINIO_ACCESS_KEY: str = "minioadmin"
    MINIO_SECRET_KEY: str = "minioadmin123"
    MINIO_SECURE: bool = False
    MINIO_BUCKET_NAME: str = "clover-files"
    
    # 移除JWT配置（个人项目无需认证）
    
    # Celery配置
    CELERY_BROKER_URL: str = "redis://localhost:6380/0"
    CELERY_RESULT_BACKEND: str = "redis://localhost:6380/0"
    
    # 文件处理配置
    MAX_FILE_SIZE: str = "100MB"
    ALLOWED_IMAGE_EXTENSIONS: str = "jpg,jpeg,png,gif,bmp,tiff"
    ALLOWED_VIDEO_EXTENSIONS: str = "mp4,avi,mov,mkv,wmv,flv"
    
    # 日志配置
    LOG_LEVEL: str = "INFO"
    LOG_FORMAT: str = "json"
    
    # CORS配置
    CORS_ORIGINS: str = "http://localhost:3000,http://localhost:5173"
    CORS_ALLOW_CREDENTIALS: bool = True
    
    # 监控配置
    ENABLE_METRICS: bool = True
    METRICS_PORT: int = 9090
    
    # 开发配置
    RELOAD: bool = True
    WORKERS: int = 1
    
    def get_cors_origins(self) -> List[str]:
        """获取CORS origins列表"""
        return [i.strip() for i in self.CORS_ORIGINS.split(",")]

    def get_allowed_image_extensions(self) -> List[str]:
        """获取允许的图像扩展名列表"""
        return [i.strip() for i in self.ALLOWED_IMAGE_EXTENSIONS.split(",")]

    def get_allowed_video_extensions(self) -> List[str]:
        """获取允许的视频扩展名列表"""
        return [i.strip() for i in self.ALLOWED_VIDEO_EXTENSIONS.split(",")]
    
    class Config:
        env_file = ".env"
        case_sensitive = True


# 创建全局配置实例
settings = Settings()


def get_settings() -> Settings:
    """获取配置实例"""
    return settings
