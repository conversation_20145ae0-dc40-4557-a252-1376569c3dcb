"""
边缘检测处理器
实现基于Canny算法的图像边缘检测功能
"""
from typing import Dict, Any
import cv2
import numpy as np
import structlog

from app.services.image_processor_base import ImageProcessorBase, ParameterConverter

logger = structlog.get_logger()


class EdgeDetectionProcessor(ImageProcessorBase):
    """边缘检测处理器"""
    
    def process_image(self, image: np.ndarray, parameters: Dict[str, Any]) -> np.ndarray:
        """
        对图像进行边缘检测
        
        Args:
            image: 输入图像（BGR格式）
            parameters: 处理参数
                - low_threshold: 低阈值，范围0-255，默认50
                - high_threshold: 高阈值，范围0-255，默认150
                - aperture_size: Sobel算子的孔径大小，3、5或7，默认3
                - l2_gradient: 是否使用L2梯度，默认False
                
        Returns:
            边缘检测后的图像（3通道，便于保存）
        """
        try:
            low_threshold = parameters.get('low_threshold', 50)
            high_threshold = parameters.get('high_threshold', 150)
            aperture_size = parameters.get('aperture_size', 3)
            l2_gradient = parameters.get('l2_gradient', False)
            
            logger.info("processing_edge_detection", 
                       input_shape=image.shape,
                       low_threshold=low_threshold,
                       high_threshold=high_threshold,
                       aperture_size=aperture_size,
                       l2_gradient=l2_gradient)
            
            # 转换为灰度图（Canny算法需要灰度图）
            if len(image.shape) == 3:
                gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            else:
                gray = image
            
            # 应用高斯模糊以减少噪声
            blurred = cv2.GaussianBlur(gray, (5, 5), 0)
            
            # 应用Canny边缘检测
            edges = cv2.Canny(
                blurred, 
                low_threshold, 
                high_threshold,
                apertureSize=aperture_size,
                L2gradient=l2_gradient
            )
            
            # 转换回3通道格式，便于保存
            edges_3channel = cv2.cvtColor(edges, cv2.COLOR_GRAY2BGR)
            
            logger.info("edge_detection_completed",
                       output_shape=edges_3channel.shape,
                       edge_pixels=np.sum(edges > 0))
            
            return edges_3channel
            
        except Exception as e:
            logger.error("edge_detection_failed", 
                        parameters=parameters,
                        error=str(e))
            raise ValueError(f"Edge detection failed: {str(e)}")


def create_edge_detection_processor(minio_service, db_session) -> EdgeDetectionProcessor:
    """
    创建边缘检测处理器实例
    
    Args:
        minio_service: MinIO服务实例
        db_session: 数据库会话
        
    Returns:
        边缘检测处理器实例
    """
    return EdgeDetectionProcessor(minio_service, db_session)


def validate_edge_detection_parameters(parameters: Dict[str, Any]) -> Dict[str, Any]:
    """
    验证和转换边缘检测处理参数
    
    Args:
        parameters: 输入参数
        
    Returns:
        验证后的参数
        
    Raises:
        ValueError: 参数无效时抛出
    """
    try:
        # 转换旧格式参数
        converted_params = ParameterConverter.convert_legacy_parameters("edge_detection", parameters)
        
        # 验证参数
        is_valid, error_msg = ParameterConverter.validate_parameters("edge_detection", converted_params)
        if not is_valid:
            raise ValueError(error_msg)
        
        # 提取和验证参数
        low_threshold = converted_params.get('low_threshold', 50)
        high_threshold = converted_params.get('high_threshold', 150)
        aperture_size = converted_params.get('aperture_size', 3)
        l2_gradient = converted_params.get('l2_gradient', False)
        
        # 类型转换和范围检查
        try:
            low_threshold = int(low_threshold)
            high_threshold = int(high_threshold)
            aperture_size = int(aperture_size)
            l2_gradient = bool(l2_gradient)
        except (ValueError, TypeError):
            raise ValueError("Invalid parameter types")
        
        # 范围检查
        if not (0 <= low_threshold <= 255):
            raise ValueError("Low threshold must be between 0 and 255")
        
        if not (0 <= high_threshold <= 255):
            raise ValueError("High threshold must be between 0 and 255")
        
        if low_threshold >= high_threshold:
            raise ValueError("Low threshold must be less than high threshold")
        
        if aperture_size not in [3, 5, 7]:
            raise ValueError("Aperture size must be 3, 5, or 7")
        
        return {
            'low_threshold': low_threshold,
            'high_threshold': high_threshold,
            'aperture_size': aperture_size,
            'l2_gradient': l2_gradient
        }
        
    except Exception as e:
        logger.error("edge_detection_parameter_validation_failed", 
                    parameters=parameters,
                    error=str(e))
        raise ValueError(f"Parameter validation failed: {str(e)}")


# 为了向后兼容，提供旧格式的函数接口
def edge_detection_legacy(image_path: str, low_threshold: int = 50, high_threshold: int = 150) -> str:
    """
    旧格式的边缘检测函数接口（向后兼容）
    
    Args:
        image_path: 图像文件路径
        low_threshold: 低阈值
        high_threshold: 高阈值
        
    Returns:
        处理后的图像路径
        
    Note:
        这个函数仅用于向后兼容，新代码应使用EdgeDetectionProcessor
    """
    logger.warning("using_legacy_edge_detection_interface", 
                  image_path=image_path,
                  low_threshold=low_threshold,
                  high_threshold=high_threshold)
    
    # 这里可以实现向旧接口的适配逻辑
    # 但在新架构中，建议直接使用EdgeDetectionProcessor
    raise NotImplementedError("Legacy interface not implemented in new architecture. Use EdgeDetectionProcessor instead.")
