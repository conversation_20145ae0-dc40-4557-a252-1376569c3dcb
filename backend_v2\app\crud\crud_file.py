"""
文件CRUD操作
"""
from typing import List, Optional, Dict, Any
from sqlalchemy.orm import Session
from sqlalchemy import func, and_, or_

from app.crud.base import CRUDBase
from app.models.file import File, FileType, FileStatus
from app.schemas.file import FileCreate, FileUpdate


class CRUDFile(CRUDBase[File, FileCreate, FileUpdate]):
    """文件CRUD操作类"""
    
    def get_by_batch_id(self, db: Session, *, batch_id: int, skip: int = 0, limit: int = 100) -> List[File]:
        """
        根据批次ID获取文件列表
        
        Args:
            db: 数据库会话
            batch_id: 批次ID
            skip: 跳过的记录数
            limit: 限制返回的记录数
            
        Returns:
            文件列表
        """
        return db.query(File).filter(File.batch_id == batch_id).offset(skip).limit(limit).all()

    def get_by_task_id(self, db: Session, *, task_id: int, skip: int = 0, limit: int = 100) -> List[File]:
        """
        根据任务ID获取文件列表

        Args:
            db: 数据库会话
            task_id: 任务ID
            skip: 跳过数量
            limit: 限制数量

        Returns:
            文件列表
        """
        # 注意：File模型中没有直接的task_id字段，需要通过batch_id关联
        # 这里假设文件通过batch关联到task，需要join查询
        from app.models.batch import Batch
        return db.query(File).join(Batch, File.batch_id == Batch.id).filter(Batch.task_id == task_id).offset(skip).limit(limit).all()

    def get_by_filename(self, db: Session, *, filename: str) -> Optional[File]:
        """
        根据文件名获取文件
        
        Args:
            db: 数据库会话
            filename: 文件名
            
        Returns:
            文件对象或None
        """
        return db.query(File).filter(File.filename == filename).first()
    
    def get_by_type(self, db: Session, *, file_type: FileType, skip: int = 0, limit: int = 100) -> List[File]:
        """
        根据文件类型获取文件列表
        
        Args:
            db: 数据库会话
            file_type: 文件类型
            skip: 跳过的记录数
            limit: 限制返回的记录数
            
        Returns:
            文件列表
        """
        return db.query(File).filter(File.file_type == file_type).offset(skip).limit(limit).all()
    
    def get_by_status(self, db: Session, *, status: FileStatus, skip: int = 0, limit: int = 100) -> List[File]:
        """
        根据状态获取文件列表
        
        Args:
            db: 数据库会话
            status: 文件状态
            skip: 跳过的记录数
            limit: 限制返回的记录数
            
        Returns:
            文件列表
        """
        return db.query(File).filter(File.status == status).offset(skip).limit(limit).all()
    
    def get_by_checksum(self, db: Session, *, checksum: str) -> Optional[File]:
        """
        根据校验和获取文件
        
        Args:
            db: 数据库会话
            checksum: 文件校验和
            
        Returns:
            文件对象或None
        """
        return db.query(File).filter(File.checksum == checksum).first()
    
    def update_status(self, db: Session, *, file_id: int, status: FileStatus) -> Optional[File]:
        """
        更新文件状态
        
        Args:
            db: 数据库会话
            file_id: 文件ID
            status: 新状态
            
        Returns:
            更新后的文件对象
        """
        file_obj = self.get(db, file_id)
        if file_obj:
            file_obj.status = status
            db.add(file_obj)
            db.commit()
            db.refresh(file_obj)
        return file_obj
    
    def update_metadata(self, db: Session, *, file_id: int, metadata: dict) -> Optional[File]:
        """
        更新文件元数据
        
        Args:
            db: 数据库会话
            file_id: 文件ID
            metadata: 元数据字典
            
        Returns:
            更新后的文件对象
        """
        file_obj = self.get(db, file_id)
        if file_obj:
            if file_obj.file_metadata:
                file_obj.file_metadata.update(metadata)
            else:
                file_obj.file_metadata = metadata
            db.add(file_obj)
            db.commit()
            db.refresh(file_obj)
        return file_obj
    
    def get_total_size_by_batch(self, db: Session, *, batch_id: int) -> int:
        """
        获取批次中所有文件的总大小

        Args:
            db: 数据库会话
            batch_id: 批次ID

        Returns:
            总文件大小（字节）
        """
        result = db.query(func.sum(File.file_size)).filter(File.batch_id == batch_id).scalar()
        return result or 0

    def find_duplicates_by_checksum(self, db: Session, *, checksum: str) -> List[File]:
        """
        根据校验和查找重复文件

        Args:
            db: 数据库会话
            checksum: 文件校验和

        Returns:
            重复文件列表
        """
        return db.query(File).filter(
            and_(
                File.checksum == checksum,
                File.status != FileStatus.DELETED
            )
        ).all()

    def find_duplicates_by_name_and_size(self, db: Session, *, filename: str, file_size: int) -> List[File]:
        """
        根据文件名和大小查找可能的重复文件

        Args:
            db: 数据库会话
            filename: 文件名
            file_size: 文件大小

        Returns:
            可能重复的文件列表
        """
        return db.query(File).filter(
            and_(
                File.filename == filename,
                File.file_size == file_size,
                File.status != FileStatus.DELETED
            )
        ).all()

    def get_file_versions(self, db: Session, *, original_filename: str, batch_id: Optional[int] = None) -> List[File]:
        """
        获取文件的所有版本

        Args:
            db: 数据库会话
            original_filename: 原始文件名
            batch_id: 批次ID（可选）

        Returns:
            文件版本列表，按创建时间排序
        """
        query = db.query(File).filter(
            and_(
                File.original_filename == original_filename,
                File.status != FileStatus.DELETED
            )
        )

        if batch_id is not None:
            query = query.filter(File.batch_id == batch_id)

        return query.order_by(File.created_at.desc()).all()

    def get_latest_version(self, db: Session, *, original_filename: str, batch_id: Optional[int] = None) -> Optional[File]:
        """
        获取文件的最新版本

        Args:
            db: 数据库会话
            original_filename: 原始文件名
            batch_id: 批次ID（可选）

        Returns:
            最新版本的文件对象
        """
        versions = self.get_file_versions(db=db, original_filename=original_filename, batch_id=batch_id)
        return versions[0] if versions else None

    def create_with_deduplication(self, db: Session, *, obj_in: FileCreate, check_duplicates: bool = True) -> File:
        """
        创建文件记录，支持去重检查

        Args:
            db: 数据库会话
            obj_in: 文件创建数据
            check_duplicates: 是否检查重复文件

        Returns:
            创建的文件对象
        """
        if check_duplicates and obj_in.checksum:
            # 检查是否存在相同校验和的文件
            existing_files = self.find_duplicates_by_checksum(db=db, checksum=obj_in.checksum)
            if existing_files:
                # 如果存在重复文件，可以选择返回现有文件或创建新的引用
                # 这里我们记录重复信息到元数据中
                metadata = obj_in.file_metadata or {}
                metadata.update({
                    "duplicate_of": existing_files[0].id,
                    "duplicate_count": len(existing_files),
                    "deduplication_checked": True
                })
                obj_in.file_metadata = metadata

        return self.create(db=db, obj_in=obj_in)


# 创建文件CRUD实例
file = CRUDFile(File)
