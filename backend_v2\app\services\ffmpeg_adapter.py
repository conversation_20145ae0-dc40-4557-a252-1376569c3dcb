"""
FFmpeg适配器
封装FFmpeg操作，提供统一的视频处理接口
基于旧框架OpenCVFFmpegProcessor的成功经验和专家评估建议设计
"""
import os
import subprocess
import tempfile
import json
import time
from typing import Dict, Any, Optional, List, Tuple
import structlog

logger = structlog.get_logger()


class FFmpegAdapter:
    """
    FFmpeg适配器类
    封装FFmpeg的复杂性，提供简洁的视频处理接口
    基于专家评估建议：强化错误诊断、音视频同步、硬件加速检测
    """

    def __init__(self):
        """初始化FFmpeg适配器"""
        self.temp_files = []
        self.hardware_accel = None
        self.ffmpeg_path = 'ffmpeg'
        self.ffprobe_path = 'ffprobe'

        # 检测FFmpeg可用性
        self._check_ffmpeg_availability()

        # 检测硬件加速
        self._detect_hardware_acceleration()
    
    def __enter__(self):
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """清理临时文件"""
        self.cleanup_temp_files()
    
    def cleanup_temp_files(self):
        """清理临时文件"""
        for temp_file in self.temp_files:
            try:
                if os.path.exists(temp_file):
                    os.remove(temp_file)
                    logger.debug("ffmpeg_temp_file_cleaned", file=temp_file)
            except Exception as e:
                logger.warning("ffmpeg_temp_file_cleanup_failed", file=temp_file, error=str(e))
        self.temp_files.clear()

    def _check_ffmpeg_availability(self):
        """检查FFmpeg和FFprobe是否可用"""
        try:
            # 检查FFmpeg
            result = subprocess.run([self.ffmpeg_path, '-version'],
                                  capture_output=True, text=True, timeout=10)
            if result.returncode != 0:
                raise RuntimeError("FFmpeg not available")

            # 检查FFprobe
            result = subprocess.run([self.ffprobe_path, '-version'],
                                  capture_output=True, text=True, timeout=10)
            if result.returncode != 0:
                raise RuntimeError("FFprobe not available")

            logger.info("ffmpeg_availability_confirmed",
                       ffmpeg_version=result.stdout.split('\n')[0])

        except (subprocess.TimeoutExpired, FileNotFoundError, RuntimeError) as e:
            logger.error("ffmpeg_not_available", error=str(e))
            raise RuntimeError(f"FFmpeg/FFprobe not available: {str(e)}")
    
    def _detect_hardware_acceleration(self) -> Optional[str]:
        """
        检测可用的硬件加速
        基于旧框架的成功经验，支持多平台检测
        """
        try:
            # 专家建议：优先检查环境变量覆盖
            env_accel = os.environ.get('FFMPEG_HARDWARE_ACCEL')
            if env_accel:
                logger.info("hardware_acceleration_override", acceleration=env_accel)
                self.hardware_accel = env_accel
                return env_accel

            # 检测NVIDIA GPU (NVENC)
            try:
                result = subprocess.run(['nvidia-smi'], capture_output=True, text=True, timeout=5)
                if result.returncode == 0:
                    # 进一步验证NVENC编码器是否可用
                    result = subprocess.run([self.ffmpeg_path, '-encoders'],
                                          capture_output=True, text=True, timeout=10)
                    if 'h264_nvenc' in result.stdout:
                        logger.info("nvidia_gpu_detected", acceleration="nvenc")
                        self.hardware_accel = 'nvenc'
                        return 'nvenc'
            except (subprocess.TimeoutExpired, FileNotFoundError):
                pass

            # 检测Intel Quick Sync (QSV)
            try:
                result = subprocess.run([self.ffmpeg_path, '-hwaccels'],
                                      capture_output=True, text=True, timeout=10)
                if 'qsv' in result.stdout:
                    # 进一步验证QSV编码器是否可用
                    result = subprocess.run([self.ffmpeg_path, '-encoders'],
                                          capture_output=True, text=True, timeout=10)
                    if 'h264_qsv' in result.stdout:
                        logger.info("intel_quicksync_detected", acceleration="qsv")
                        self.hardware_accel = 'qsv'
                        return 'qsv'
            except (subprocess.TimeoutExpired, FileNotFoundError):
                pass

            # 检测VAAPI (Linux)
            try:
                if os.path.exists('/dev/dri'):
                    result = subprocess.run([self.ffmpeg_path, '-hwaccels'],
                                          capture_output=True, text=True, timeout=10)
                    if 'vaapi' in result.stdout:
                        logger.info("vaapi_detected", acceleration="vaapi")
                        self.hardware_accel = 'vaapi'
                        return 'vaapi'
            except Exception:
                pass

            logger.debug("no_hardware_acceleration_detected", fallback="software")
            self.hardware_accel = None
            return None

        except Exception as e:
            logger.warning("hardware_acceleration_detection_failed", error=str(e))
            self.hardware_accel = None
            return None

    def _capture_ffmpeg_stderr(self, cmd: List[str], timeout: int = 300) -> Tuple[int, str, str]:
        """
        执行FFmpeg命令并捕获完整的stderr输出
        专家强调：这是诊断失败的唯一可靠信息源

        Args:
            cmd: FFmpeg命令列表
            timeout: 超时时间（秒）

        Returns:
            (返回码, stdout, stderr)
        """
        try:
            logger.info("executing_ffmpeg_command", command=' '.join(cmd))

            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=timeout,
                encoding='utf-8',
                errors='replace'  # 处理编码错误
            )

            # 记录详细的执行结果
            logger.info("ffmpeg_command_completed",
                       return_code=result.returncode,
                       stdout_length=len(result.stdout),
                       stderr_length=len(result.stderr))

            # 如果失败，记录完整的stderr
            if result.returncode != 0:
                logger.error("ffmpeg_command_failed",
                           return_code=result.returncode,
                           stderr=result.stderr,
                           command=' '.join(cmd))

            return result.returncode, result.stdout, result.stderr

        except subprocess.TimeoutExpired as e:
            error_msg = f"FFmpeg command timed out after {timeout} seconds"
            logger.error("ffmpeg_command_timeout",
                        timeout=timeout,
                        command=' '.join(cmd))
            return -1, "", error_msg

        except Exception as e:
            error_msg = f"FFmpeg command execution failed: {str(e)}"
            logger.error("ffmpeg_command_execution_failed",
                        error=str(e),
                        command=' '.join(cmd))
            return -1, "", error_msg
    
    def get_video_info(self, video_path: str) -> Dict[str, Any]:
        """
        使用FFprobe获取详细的视频信息
        包括音视频流信息、编码格式、时间戳等
        """
        try:
            cmd = [
                self.ffprobe_path,
                '-v', 'quiet',
                '-print_format', 'json',
                '-show_format',
                '-show_streams',
                video_path
            ]

            return_code, stdout, stderr = self._capture_ffmpeg_stderr(cmd, timeout=30)
            if return_code != 0:
                raise RuntimeError(f"FFprobe failed: {stderr}")

            probe_data = json.loads(stdout)
            
            # 提取视频流信息
            video_stream = None
            audio_stream = None
            
            for stream in probe_data.get('streams', []):
                if stream.get('codec_type') == 'video' and video_stream is None:
                    video_stream = stream
                elif stream.get('codec_type') == 'audio' and audio_stream is None:
                    audio_stream = stream
            
            if not video_stream:
                raise ValueError("No video stream found")
            
            # 构建视频信息
            video_info = {
                'width': int(video_stream.get('width', 0)),
                'height': int(video_stream.get('height', 0)),
                'fps': self._parse_fps(video_stream.get('r_frame_rate', '0/1')),
                'duration': float(video_stream.get('duration', 0)),
                'total_frames': int(video_stream.get('nb_frames', 0)),
                'codec': video_stream.get('codec_name', 'unknown'),
                'pixel_format': video_stream.get('pix_fmt', 'unknown'),
                'bitrate': int(video_stream.get('bit_rate', 0)),
                'has_audio': audio_stream is not None,
                'audio_codec': audio_stream.get('codec_name') if audio_stream else None,
                'format': probe_data.get('format', {}).get('format_name', 'unknown'),
                # 专家建议：添加时间戳信息用于同步分析
                'start_time': float(video_stream.get('start_time', 0)),
                'time_base': video_stream.get('time_base', '1/1000'),
            }
            
            # 如果没有nb_frames，尝试从duration和fps计算
            if video_info['total_frames'] == 0 and video_info['duration'] > 0 and video_info['fps'] > 0:
                video_info['total_frames'] = int(video_info['duration'] * video_info['fps'])
            
            logger.info("video_info_extracted", video_info=video_info)
            return video_info
            
        except Exception as e:
            logger.error("video_info_extraction_failed", video_path=video_path, error=str(e))
            raise ValueError(f"Failed to extract video info: {str(e)}")
    
    def _parse_fps(self, fps_str: str) -> float:
        """解析帧率字符串 (例如: "30/1" -> 30.0)"""
        try:
            if '/' in fps_str:
                num, den = fps_str.split('/')
                return float(num) / float(den) if float(den) != 0 else 0.0
            else:
                return float(fps_str)
        except (ValueError, ZeroDivisionError):
            return 0.0

    def extract_audio(self, video_path: str) -> Optional[str]:
        """
        提取音频流到临时文件

        Args:
            video_path: 输入视频路径

        Returns:
            音频文件路径，如果没有音频则返回None
        """
        try:
            # 检查是否有音频流
            video_info = self.get_video_info(video_path)
            if not video_info['has_audio']:
                logger.info("no_audio_stream_found", video_path=video_path)
                return None

            # 创建临时音频文件
            temp_fd, audio_path = tempfile.mkstemp(suffix='.aac')
            os.close(temp_fd)
            self.temp_files.append(audio_path)

            # 提取音频
            cmd = [
                self.ffmpeg_path, '-y',
                '-i', video_path,
                '-vn',  # 不包含视频
                '-acodec', 'copy',  # 复制音频编码
                audio_path
            ]

            return_code, stdout, stderr = self._capture_ffmpeg_stderr(cmd, timeout=60)
            if return_code != 0:
                logger.warning("audio_extraction_failed", error=stderr)
                return None

            logger.info("audio_extracted_successfully", audio_path=audio_path)
            return audio_path

        except Exception as e:
            logger.error("audio_extraction_error", video_path=video_path, error=str(e))
            return None

    def merge_audio_video(
        self,
        video_path: str,
        audio_path: str,
        output_path: str,
        enable_sync_enhancement: bool = True
    ) -> bool:
        """
        合并音频和视频
        基于旧框架的成功经验，支持硬件加速
        专家建议：加入-async 1处理音频漂移

        Args:
            video_path: 视频文件路径
            audio_path: 音频文件路径
            output_path: 输出文件路径
            enable_sync_enhancement: 是否启用同步增强

        Returns:
            是否成功
        """
        try:
            # 构建FFmpeg命令
            cmd = [
                self.ffmpeg_path, '-y',
                '-i', video_path,
                '-i', audio_path,
                '-c:v', 'copy',  # 复制视频流，不重编码
                '-c:a', 'aac',   # 音频编码为AAC
                '-map', '0:v:0', # 使用第一个文件的视频流
                '-map', '1:a:0', # 使用第二个文件的音频流
                '-shortest',     # 以较短的流为准 - 关键的同步机制
                '-preset', 'fast',  # 快速预设
            ]

            # 专家建议：添加音频同步增强
            if enable_sync_enhancement:
                cmd.extend(['-async', '1'])  # 处理音频漂移

            # 添加硬件加速（如果可用）
            if self.hardware_accel:
                cmd.insert(1, '-hwaccel')
                cmd.insert(2, self.hardware_accel)

            cmd.append(output_path)

            logger.info("merging_audio_video", command=' '.join(cmd))

            return_code, stdout, stderr = self._capture_ffmpeg_stderr(cmd, timeout=300)
            if return_code != 0:
                logger.error("audio_video_merge_failed", error=stderr)
                return False

            logger.info("audio_video_merged_successfully", output_path=output_path)
            return True

        except Exception as e:
            logger.error("audio_video_merge_error", error=str(e))
            return False
    
    def extract_audio(self, video_path: str) -> Optional[str]:
        """
        提取音频流到临时文件
        
        Args:
            video_path: 输入视频路径
            
        Returns:
            音频文件路径，如果没有音频则返回None
        """
        try:
            # 检查是否有音频流
            video_info = self.get_video_info(video_path)
            if not video_info['has_audio']:
                logger.info("no_audio_stream_found", video_path=video_path)
                return None
            
            # 创建临时音频文件
            temp_fd, audio_path = tempfile.mkstemp(suffix='.aac')
            os.close(temp_fd)
            self.temp_files.append(audio_path)
            
            # 提取音频
            cmd = [
                self.ffmpeg_path, '-y',
                '-i', video_path,
                '-vn',  # 不包含视频
                '-acodec', 'copy',  # 复制音频编码
                audio_path
            ]
            
            return_code, stdout, stderr = self._capture_ffmpeg_stderr(cmd, timeout=60)
            if return_code != 0:
                logger.warning("audio_extraction_failed", error=stderr)
                return None
            
            logger.info("audio_extracted_successfully", audio_path=audio_path)
            return audio_path
            
        except Exception as e:
            logger.error("audio_extraction_error", video_path=video_path, error=str(e))
            return None
    
    def merge_audio_video(
        self,
        video_path: str,
        audio_path: str,
        output_path: str,
        enable_sync_enhancement: bool = True
    ) -> bool:
        """
        合并音频和视频
        基于旧框架的成功经验，支持硬件加速
        专家建议：加入-async 1处理音频漂移

        Args:
            video_path: 视频文件路径
            audio_path: 音频文件路径
            output_path: 输出文件路径
            enable_sync_enhancement: 是否启用同步增强

        Returns:
            是否成功
        """
        try:
            # 构建FFmpeg命令
            cmd = [
                self.ffmpeg_path, '-y',
                '-i', video_path,
                '-i', audio_path,
                '-c:v', 'copy',  # 复制视频流，不重编码
                '-c:a', 'aac',   # 音频编码为AAC
                '-map', '0:v:0', # 使用第一个文件的视频流
                '-map', '1:a:0', # 使用第二个文件的音频流
                '-shortest',     # 以较短的流为准 - 关键的同步机制
                '-preset', 'fast',  # 快速预设
            ]

            # 专家建议：添加音频同步增强
            if enable_sync_enhancement:
                cmd.extend(['-async', '1'])  # 处理音频漂移

            # 添加硬件加速（如果可用）
            if self.hardware_accel:
                cmd.insert(1, '-hwaccel')
                # 修复硬件加速参数映射
                if self.hardware_accel == 'nvenc':
                    cmd.insert(2, 'cuda')  # NVENC使用cuda作为hwaccel参数
                elif self.hardware_accel == 'qsv':
                    cmd.insert(2, 'qsv')
                elif self.hardware_accel == 'vaapi':
                    cmd.insert(2, 'vaapi')
                else:
                    cmd.insert(2, self.hardware_accel)

            cmd.append(output_path)
            
            logger.info("merging_audio_video", command=' '.join(cmd))

            return_code, stdout, stderr = self._capture_ffmpeg_stderr(cmd, timeout=300)
            if return_code != 0:
                logger.error("audio_video_merge_failed", error=stderr)
                return False

            logger.info("audio_video_merged_successfully", output_path=output_path)
            return True
            
        except Exception as e:
            logger.error("audio_video_merge_error", error=str(e))
            return False
    
    def convert_video_format(
        self,
        input_path: str,
        output_path: str,
        codec: str = 'libx264',
        preset: str = 'fast',
        crf: int = 23,
        pixel_format: str = 'yuv420p',
        width: Optional[int] = None,
        height: Optional[int] = None,
        fps: Optional[float] = None
    ) -> bool:
        """
        转换视频格式
        支持各种编码格式和硬件加速

        Args:
            input_path: 输入视频路径
            output_path: 输出视频路径
            codec: 视频编码器
            preset: 编码预设
            crf: 质量设置 (0-51, 越小质量越好)
            pixel_format: 像素格式
            width: 目标宽度（可选）
            height: 目标高度（可选）
            fps: 目标帧率（可选）

        Returns:
            是否成功
        """
        try:
            # 构建基础命令
            cmd = [
                self.ffmpeg_path, '-y',
                '-i', input_path,
            ]

            # 添加硬件加速（输入端）
            if self.hardware_accel:
                if self.hardware_accel == 'nvenc':
                    cmd.insert(1, '-hwaccel')
                    cmd.insert(2, 'cuda')
                elif self.hardware_accel == 'qsv':
                    cmd.insert(1, '-hwaccel')
                    cmd.insert(2, 'qsv')
                elif self.hardware_accel == 'vaapi':
                    cmd.insert(1, '-hwaccel')
                    cmd.insert(2, 'vaapi')

            # 构建视频滤镜
            video_filters = []

            # 添加尺寸调整
            if width and height:
                video_filters.append(f'scale={width}:{height}')

            # 添加帧率调整
            if fps:
                video_filters.append(f'fps={fps}')

            # 应用视频滤镜
            if video_filters:
                cmd.extend(['-vf', ','.join(video_filters)])

            # 选择合适的编码器（基于硬件加速）
            if self.hardware_accel == 'nvenc' and codec in ['h264', 'libx264']:
                actual_codec = 'h264_nvenc'
            elif self.hardware_accel == 'qsv' and codec in ['h264', 'libx264']:
                actual_codec = 'h264_qsv'
            elif self.hardware_accel == 'nvenc' and codec in ['h265', 'libx265', 'hevc']:
                actual_codec = 'hevc_nvenc'
            elif self.hardware_accel == 'qsv' and codec in ['h265', 'libx265', 'hevc']:
                actual_codec = 'hevc_qsv'
            else:
                actual_codec = codec

            # 添加编码参数
            cmd.extend(['-c:v', actual_codec])

            # 添加编码质量参数
            if 'nvenc' in actual_codec:
                # NVENC特定参数
                cmd.extend(['-preset', 'fast', '-cq', str(crf)])
            elif 'qsv' in actual_codec:
                # QSV特定参数
                cmd.extend(['-preset', 'fast', '-global_quality', str(crf)])
            else:
                # 软件编码参数
                cmd.extend(['-preset', preset, '-crf', str(crf)])

            # 添加像素格式
            cmd.extend(['-pix_fmt', pixel_format])

            # 添加输出路径
            cmd.append(output_path)

            logger.info("converting_video_format",
                       command=' '.join(cmd),
                       codec=actual_codec,
                       hardware_accel=self.hardware_accel)

            return_code, stdout, stderr = self._capture_ffmpeg_stderr(cmd, timeout=600)
            if return_code != 0:
                logger.error("video_format_conversion_failed", error=stderr)
                return False

            logger.info("video_format_converted_successfully",
                       output_path=output_path,
                       codec=actual_codec)
            return True

        except Exception as e:
            logger.error("video_format_conversion_error", error=str(e))
            return False
