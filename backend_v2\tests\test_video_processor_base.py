#!/usr/bin/env python3
"""
测试视频处理基础架构
验证VideoProcessorBase和VideoGrayscaleProcessor的基本功能
"""
import sys
import os
import numpy as np
import cv2
import tempfile
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_video_processor_imports():
    """测试视频处理器导入"""
    print("🧪 测试视频处理器导入...")
    
    try:
        from app.services.video_processor_base import VideoProcessorBase, VideoParameterConverter
        from app.services.video_processors import (
            VideoGrayscaleProcessor, 
            create_video_grayscale_processor, 
            validate_video_grayscale_parameters
        )
        print("✅ 所有视频处理器导入成功")
        return True
        
    except Exception as e:
        print(f"❌ 视频处理器导入失败: {str(e)}")
        return False

def test_video_parameter_converter():
    """测试视频参数转换器"""
    print("\n🧪 测试视频参数转换器...")
    
    try:
        from app.services.video_processor_base import VideoParameterConverter
        
        # 测试视频灰度转换参数
        params = VideoParameterConverter.convert_legacy_video_parameters("video_grayscale", {})
        print("✅ 视频灰度转换参数转换成功")
        
        # 测试视频尺寸调整参数
        resize_params = VideoParameterConverter.convert_legacy_video_parameters(
            "video_resize", 
            {"width": "640", "height": "480"}
        )
        if resize_params["width"] == 640 and resize_params["height"] == 480:
            print("✅ 视频尺寸调整参数转换成功")
        else:
            print("❌ 视频尺寸调整参数转换失败")
            return False
        
        # 测试参数验证
        is_valid, error_msg = VideoParameterConverter.validate_video_parameters("video_resize", resize_params)
        if is_valid:
            print("✅ 视频参数验证成功")
        else:
            print(f"❌ 视频参数验证失败: {error_msg}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 视频参数转换器测试失败: {str(e)}")
        return False

def test_video_info_extraction():
    """测试视频信息提取"""
    print("\n🧪 测试视频信息提取...")
    
    try:
        from app.services.video_processor_base import VideoProcessorBase
        from app.services.minio_service import get_minio_service
        from app.core.database import get_db_session
        
        # 创建一个简单的测试视频
        temp_fd, test_video_path = tempfile.mkstemp(suffix=".mp4")
        os.close(temp_fd)
        
        try:
            # 创建一个简单的测试视频（10帧，640x480）
            fourcc = cv2.VideoWriter_fourcc(*'mp4v')
            out = cv2.VideoWriter(test_video_path, fourcc, 30.0, (640, 480))
            
            # 写入10帧测试图像
            for i in range(10):
                frame = np.full((480, 640, 3), i * 25, dtype=np.uint8)
                out.write(frame)
            
            out.release()
            
            # 测试视频信息提取
            db = get_db_session()
            minio_service = get_minio_service()
            
            try:
                processor = VideoProcessorBase(minio_service, db)
                video_info = processor.get_video_info(test_video_path)
                
                if video_info["total_frames"] == 10:
                    print("✅ 视频帧数提取正确")
                else:
                    print(f"❌ 视频帧数错误: {video_info['total_frames']}")
                    return False
                
                if video_info["width"] == 640 and video_info["height"] == 480:
                    print("✅ 视频分辨率提取正确")
                else:
                    print(f"❌ 视频分辨率错误: {video_info['width']}x{video_info['height']}")
                    return False
                
                if video_info["fps"] == 30.0:
                    print("✅ 视频帧率提取正确")
                else:
                    print(f"❌ 视频帧率错误: {video_info['fps']}")
                    return False
                
                return True
                
            finally:
                db.close()
        
        finally:
            # 清理测试文件
            if os.path.exists(test_video_path):
                os.remove(test_video_path)
        
    except Exception as e:
        print(f"❌ 视频信息提取测试失败: {str(e)}")
        return False

def test_video_grayscale_processor():
    """测试视频灰度转换处理器"""
    print("\n🧪 测试视频灰度转换处理器...")
    
    try:
        from app.services.video_processors import VideoGrayscaleProcessor
        from app.services.minio_service import get_minio_service
        from app.core.database import get_db_session
        
        # 创建测试图像帧
        test_frame = np.random.randint(0, 255, (100, 100, 3), dtype=np.uint8)
        
        # 创建处理器
        db = get_db_session()
        minio_service = get_minio_service()
        
        try:
            processor = VideoGrayscaleProcessor(minio_service, db)
            
            # 测试单帧处理
            result_frame = processor.process_image(test_frame, {})
            
            if result_frame.shape == test_frame.shape:
                print("✅ 视频灰度转换输出形状正确")
            else:
                print(f"❌ 输出形状错误: {result_frame.shape}")
                return False
            
            # 检查是否为灰度图（所有通道值应该相等）
            if np.allclose(result_frame[:,:,0], result_frame[:,:,1]) and np.allclose(result_frame[:,:,1], result_frame[:,:,2]):
                print("✅ 灰度转换效果正确")
            else:
                print("❌ 灰度转换效果不正确")
                return False
            
            return True
            
        finally:
            db.close()
        
    except Exception as e:
        print(f"❌ 视频灰度转换处理器测试失败: {str(e)}")
        return False

def test_task_imports():
    """测试任务导入"""
    print("\n🧪 测试任务导入...")
    
    try:
        from app.tasks.video_tasks import process_video_grayscale
        
        # 检查任务是否正确注册
        if hasattr(process_video_grayscale, 'name'):
            print(f"✅ 视频灰度转换任务已注册: {process_video_grayscale.name}")
        else:
            print("❌ 视频灰度转换任务未正确注册")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 任务导入测试失败: {str(e)}")
        return False

def test_error_handling():
    """测试错误处理"""
    print("\n🧪 测试错误处理...")
    
    try:
        from app.services.video_processor_base import VideoParameterConverter
        
        # 测试无效参数
        test_cases = [
            ("video_resize", {"width": -100, "height": 480}, "负宽度"),
            ("video_resize", {"width": 640, "height": 5000}, "超大高度"),
        ]
        
        for operation, params, description in test_cases:
            is_valid, error_msg = VideoParameterConverter.validate_video_parameters(operation, params)
            if is_valid:
                print(f"❌ {description}参数未被拒绝")
                return False
            else:
                print(f"✅ {description}参数正确被拒绝: {error_msg}")
        
        return True
        
    except Exception as e:
        print(f"❌ 错误处理测试失败: {str(e)}")
        return False

if __name__ == "__main__":
    print("🚀 开始视频处理基础架构测试...")
    
    success1 = test_video_processor_imports()
    success2 = test_video_parameter_converter()
    success3 = test_video_info_extraction()
    success4 = test_video_grayscale_processor()
    success5 = test_task_imports()
    success6 = test_error_handling()
    
    if success1 and success2 and success3 and success4 and success5 and success6:
        print("\n🎉 所有视频处理基础架构测试通过！")
        print("\n📋 2.4.1任务验证完成:")
        print("✅ 视频处理器导入：VideoProcessorBase和VideoGrayscaleProcessor正确导入")
        print("✅ 参数转换器：VideoParameterConverter参数转换和验证正常")
        print("✅ 视频信息提取：正确提取帧数、分辨率、帧率等信息")
        print("✅ 灰度转换算法：单帧灰度转换效果正确")
        print("✅ 任务集成：Celery任务正确注册")
        print("✅ 错误处理：无效参数正确被拒绝")
        print("\n🎯 2.4.1 VideoProcessorBase基础架构建立完成！")
        sys.exit(0)
    else:
        print("\n💥 视频处理基础架构测试失败！")
        sys.exit(1)
