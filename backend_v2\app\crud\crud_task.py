"""
任务CRUD操作
"""
from typing import List, Optional
from sqlalchemy.orm import Session

from app.crud.base import CRUDBase
from app.models.task import Task, TaskStatus
from app.schemas.task import TaskCreate, TaskUpdate


class CRUDTask(CRUDBase[Task, TaskCreate, TaskUpdate]):
    """任务CRUD操作类"""
    
    def get_by_name(self, db: Session, *, name: str) -> Optional[Task]:
        """
        根据名称获取任务
        
        Args:
            db: 数据库会话
            name: 任务名称
            
        Returns:
            任务对象或None
        """
        return db.query(Task).filter(Task.name == name).first()
    
    def get_by_status(self, db: Session, *, status: TaskStatus, skip: int = 0, limit: int = 100) -> List[Task]:
        """
        根据状态获取任务列表
        
        Args:
            db: 数据库会话
            status: 任务状态
            skip: 跳过的记录数
            limit: 限制返回的记录数
            
        Returns:
            任务列表
        """
        return db.query(Task).filter(Task.status == status).offset(skip).limit(limit).all()
    
    def get_active_tasks(self, db: Session, *, skip: int = 0, limit: int = 100) -> List[Task]:
        """
        获取活跃的任务列表
        
        Args:
            db: 数据库会话
            skip: 跳过的记录数
            limit: 限制返回的记录数
            
        Returns:
            活跃任务列表
        """
        active_statuses = [TaskStatus.PENDING, TaskStatus.IN_PROGRESS]
        return db.query(Task).filter(Task.status.in_(active_statuses)).offset(skip).limit(limit).all()
    
    def update_progress(self, db: Session, *, task_id: int, progress: float) -> Optional[Task]:
        """
        更新任务进度
        
        Args:
            db: 数据库会话
            task_id: 任务ID
            progress: 进度值(0-100)
            
        Returns:
            更新后的任务对象
        """
        task = self.get(db, task_id)
        if task:
            task.progress = max(0, min(100, progress))  # 确保进度在0-100之间
            db.add(task)
            db.commit()
            db.refresh(task)
        return task
    
    def update_status(self, db: Session, *, task_id: int, status: TaskStatus, error_message: Optional[str] = None) -> Optional[Task]:
        """
        更新任务状态
        
        Args:
            db: 数据库会话
            task_id: 任务ID
            status: 新状态
            error_message: 错误信息（可选）
            
        Returns:
            更新后的任务对象
        """
        task = self.get(db, task_id)
        if task:
            task.status = status
            if error_message:
                task.error_message = error_message
            db.add(task)
            db.commit()
            db.refresh(task)
        return task
    
    def increment_completed_batches(self, db: Session, *, task_id: int) -> Optional[Task]:
        """
        增加已完成批次数
        
        Args:
            db: 数据库会话
            task_id: 任务ID
            
        Returns:
            更新后的任务对象
        """
        task = self.get(db, task_id)
        if task:
            task.completed_batches += 1
            # 自动更新进度
            if task.total_batches > 0:
                task.progress = (task.completed_batches / task.total_batches) * 100
            db.add(task)
            db.commit()
            db.refresh(task)
        return task
    
    def increment_failed_batches(self, db: Session, *, task_id: int) -> Optional[Task]:
        """
        增加失败批次数
        
        Args:
            db: 数据库会话
            task_id: 任务ID
            
        Returns:
            更新后的任务对象
        """
        task = self.get(db, task_id)
        if task:
            task.failed_batches += 1
            db.add(task)
            db.commit()
            db.refresh(task)
        return task

    def count_all(self, db: Session) -> int:
        """
        获取所有任务的总数

        Args:
            db: 数据库会话

        Returns:
            任务总数
        """
        return db.query(Task).count()

    def count_by_status(self, db: Session, *, status: TaskStatus, name_filter: Optional[str] = None) -> int:
        """
        根据状态和名称过滤获取任务数量

        Args:
            db: 数据库会话
            status: 任务状态
            name_filter: 名称过滤（可选）

        Returns:
            符合条件的任务数量
        """
        query = db.query(Task).filter(Task.status == status)
        if name_filter:
            query = query.filter(Task.name.ilike(f"%{name_filter}%"))
        return query.count()

    def count_with_filters(self, db: Session, *, name_filter: Optional[str] = None) -> int:
        """
        根据过滤条件获取任务数量

        Args:
            db: 数据库会话
            name_filter: 名称过滤（可选）

        Returns:
            符合条件的任务数量
        """
        query = db.query(Task)
        if name_filter:
            query = query.filter(Task.name.ilike(f"%{name_filter}%"))
        return query.count()

    def get_by_status_with_pagination(
        self,
        db: Session,
        *,
        status: TaskStatus,
        skip: int = 0,
        limit: int = 100,
        name_filter: Optional[str] = None,
        sort_by: str = "created_at",
        sort_order: str = "desc"
    ) -> List[Task]:
        """
        根据状态分页获取任务列表（增强版）

        Args:
            db: 数据库会话
            status: 任务状态
            skip: 跳过的记录数
            limit: 限制返回的记录数
            name_filter: 名称过滤（可选）
            sort_by: 排序字段
            sort_order: 排序顺序

        Returns:
            任务列表
        """
        query = db.query(Task).filter(Task.status == status)

        # 名称过滤
        if name_filter:
            query = query.filter(Task.name.ilike(f"%{name_filter}%"))

        # 排序
        sort_column = getattr(Task, sort_by, Task.created_at)
        if sort_order == "desc":
            query = query.order_by(sort_column.desc())
        else:
            query = query.order_by(sort_column.asc())

        return query.offset(skip).limit(limit).all()

    def get_multi_with_filters(
        self,
        db: Session,
        *,
        skip: int = 0,
        limit: int = 100,
        name_filter: Optional[str] = None,
        sort_by: str = "created_at",
        sort_order: str = "desc"
    ) -> List[Task]:
        """
        根据过滤条件分页获取任务列表

        Args:
            db: 数据库会话
            skip: 跳过的记录数
            limit: 限制返回的记录数
            name_filter: 名称过滤（可选）
            sort_by: 排序字段
            sort_order: 排序顺序

        Returns:
            任务列表
        """
        query = db.query(Task)

        # 名称过滤
        if name_filter:
            query = query.filter(Task.name.ilike(f"%{name_filter}%"))

        # 排序
        sort_column = getattr(Task, sort_by, Task.created_at)
        if sort_order == "desc":
            query = query.order_by(sort_column.desc())
        else:
            query = query.order_by(sort_column.asc())

        return query.offset(skip).limit(limit).all()


# 创建任务CRUD实例
task = CRUDTask(Task)
