"""
任务状态转换服务
提供任务状态转换的业务逻辑和验证
"""

from typing import Dict, List, Optional, Tuple
from enum import Enum
import structlog
from datetime import datetime

from app.models.task import TaskStatus
from app.models.batch import BatchStatus

logger = structlog.get_logger()


class StateTransitionError(Exception):
    """状态转换错误"""
    pass


class TaskStateTransitionService:
    """任务状态转换服务"""
    
    # 定义允许的状态转换规则
    ALLOWED_TRANSITIONS: Dict[TaskStatus, List[TaskStatus]] = {
        TaskStatus.PENDING: [
            TaskStatus.IN_PROGRESS,  # 开始处理
            TaskStatus.CANCELLED     # 直接取消
        ],
        TaskStatus.IN_PROGRESS: [
            TaskStatus.PAUSED,       # 暂停处理
            TaskStatus.CANCELLING,   # 请求取消
            TaskStatus.SUCCESS,      # 成功完成
            TaskStatus.FAILED,       # 处理失败
            TaskStatus.PARTIAL       # 部分完成
        ],
        TaskStatus.PAUSED: [
            TaskStatus.IN_PROGRESS,  # 恢复处理
            TaskStatus.CANCELLING    # 请求取消
        ],
        TaskStatus.CANCELLING: [
            TaskStatus.CANCELLED     # 取消完成
        ],
        # 终态不允许转换
        TaskStatus.CANCELLED: [],
        TaskStatus.SUCCESS: [],
        TaskStatus.FAILED: [],
        TaskStatus.PARTIAL: []
    }
    
    # 状态描述
    STATUS_DESCRIPTIONS: Dict[TaskStatus, str] = {
        TaskStatus.PENDING: "等待处理",
        TaskStatus.IN_PROGRESS: "正在处理",
        TaskStatus.PAUSED: "已暂停",
        TaskStatus.CANCELLING: "正在取消",
        TaskStatus.CANCELLED: "已取消",
        TaskStatus.SUCCESS: "成功完成",
        TaskStatus.FAILED: "处理失败",
        TaskStatus.PARTIAL: "部分完成"
    }
    
    @classmethod
    def validate_transition(
        cls, 
        current_status: TaskStatus, 
        target_status: TaskStatus
    ) -> Tuple[bool, Optional[str]]:
        """
        验证状态转换是否允许
        
        Args:
            current_status: 当前状态
            target_status: 目标状态
            
        Returns:
            (是否允许, 错误信息)
        """
        if current_status == target_status:
            return False, f"任务已处于 {cls.STATUS_DESCRIPTIONS[current_status]} 状态"
        
        allowed_targets = cls.ALLOWED_TRANSITIONS.get(current_status, [])
        if target_status not in allowed_targets:
            return False, (
                f"不允许从 {cls.STATUS_DESCRIPTIONS[current_status]} "
                f"转换到 {cls.STATUS_DESCRIPTIONS[target_status]}"
            )
        
        return True, None
    
    @classmethod
    def get_allowed_transitions(cls, current_status: TaskStatus) -> List[TaskStatus]:
        """
        获取当前状态允许的转换目标
        
        Args:
            current_status: 当前状态
            
        Returns:
            允许的目标状态列表
        """
        return cls.ALLOWED_TRANSITIONS.get(current_status, [])
    
    @classmethod
    def is_terminal_status(cls, status: TaskStatus) -> bool:
        """
        检查是否为终态
        
        Args:
            status: 状态
            
        Returns:
            是否为终态
        """
        return status in [TaskStatus.SUCCESS, TaskStatus.FAILED, TaskStatus.CANCELLED, TaskStatus.PARTIAL]
    
    @classmethod
    def is_active_status(cls, status: TaskStatus) -> bool:
        """
        检查是否为活跃状态
        
        Args:
            status: 状态
            
        Returns:
            是否为活跃状态
        """
        return status in [TaskStatus.PENDING, TaskStatus.IN_PROGRESS]
    
    @classmethod
    def can_be_paused(cls, status: TaskStatus) -> bool:
        """
        检查是否可以暂停
        
        Args:
            status: 状态
            
        Returns:
            是否可以暂停
        """
        return status == TaskStatus.IN_PROGRESS
    
    @classmethod
    def can_be_resumed(cls, status: TaskStatus) -> bool:
        """
        检查是否可以恢复
        
        Args:
            status: 状态
            
        Returns:
            是否可以恢复
        """
        return status == TaskStatus.PAUSED
    
    @classmethod
    def can_be_cancelled(cls, status: TaskStatus) -> bool:
        """
        检查是否可以取消
        
        Args:
            status: 状态
            
        Returns:
            是否可以取消
        """
        return status in [TaskStatus.PENDING, TaskStatus.IN_PROGRESS, TaskStatus.PAUSED]
    
    @classmethod
    def get_transition_reason(cls, current_status: TaskStatus, target_status: TaskStatus) -> str:
        """
        获取状态转换的原因描述
        
        Args:
            current_status: 当前状态
            target_status: 目标状态
            
        Returns:
            转换原因描述
        """
        transition_reasons = {
            (TaskStatus.PENDING, TaskStatus.IN_PROGRESS): "开始处理任务",
            (TaskStatus.IN_PROGRESS, TaskStatus.PAUSED): "用户暂停任务",
            (TaskStatus.PAUSED, TaskStatus.IN_PROGRESS): "用户恢复任务",
            (TaskStatus.IN_PROGRESS, TaskStatus.CANCELLING): "用户请求取消任务",
            (TaskStatus.CANCELLING, TaskStatus.CANCELLED): "任务取消完成",
            (TaskStatus.IN_PROGRESS, TaskStatus.SUCCESS): "任务处理成功",
            (TaskStatus.IN_PROGRESS, TaskStatus.FAILED): "任务处理失败",
            (TaskStatus.IN_PROGRESS, TaskStatus.PARTIAL): "任务部分完成",
            (TaskStatus.PENDING, TaskStatus.CANCELLED): "任务被直接取消"
        }
        
        return transition_reasons.get(
            (current_status, target_status), 
            f"状态从 {cls.STATUS_DESCRIPTIONS[current_status]} 转换到 {cls.STATUS_DESCRIPTIONS[target_status]}"
        )
    
    @classmethod
    def create_transition_log(
        cls,
        task_id: int,
        current_status: TaskStatus,
        target_status: TaskStatus,
        reason: Optional[str] = None,
        user_id: Optional[int] = None
    ) -> Dict:
        """
        创建状态转换日志
        
        Args:
            task_id: 任务ID
            current_status: 当前状态
            target_status: 目标状态
            reason: 转换原因
            user_id: 用户ID
            
        Returns:
            转换日志字典
        """
        if not reason:
            reason = cls.get_transition_reason(current_status, target_status)
        
        return {
            "task_id": task_id,
            "from_status": current_status.value,
            "to_status": target_status.value,
            "reason": reason,
            "user_id": user_id,
            "timestamp": datetime.now().isoformat(),
            "transition_type": "manual" if user_id else "automatic"
        }


class BatchStateTransitionService:
    """批次状态转换服务"""
    
    # 批次状态转换规则
    ALLOWED_TRANSITIONS: Dict[BatchStatus, List[BatchStatus]] = {
        BatchStatus.PENDING: [
            BatchStatus.IN_PROGRESS,  # 开始处理
            BatchStatus.ABORTED       # 直接中止
        ],
        BatchStatus.IN_PROGRESS: [
            BatchStatus.SUCCESS,      # 成功完成
            BatchStatus.FAILED,       # 处理失败
            BatchStatus.ABORTED       # 中止处理
        ],
        # 终态不允许转换
        BatchStatus.SUCCESS: [],
        BatchStatus.FAILED: [],
        BatchStatus.ABORTED: []
    }
    
    @classmethod
    def validate_transition(
        cls, 
        current_status: BatchStatus, 
        target_status: BatchStatus
    ) -> Tuple[bool, Optional[str]]:
        """验证批次状态转换"""
        if current_status == target_status:
            return False, f"批次已处于 {current_status.value} 状态"
        
        allowed_targets = cls.ALLOWED_TRANSITIONS.get(current_status, [])
        if target_status not in allowed_targets:
            return False, f"不允许从 {current_status.value} 转换到 {target_status.value}"
        
        return True, None
