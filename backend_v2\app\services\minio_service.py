"""
MinIO对象存储服务
"""
from typing import Optional, BinaryIO, Dict, Any, List
from minio import Minio
from minio.error import S3Error
import structlog
from urllib.parse import urljoin
import io
from datetime import timedelta

from app.core.config import settings
from app.utils.storage_path import StoragePathManager, StorageType

logger = structlog.get_logger()


class MinIOService:
    """MinIO对象存储服务类"""
    
    def __init__(self):
        """初始化MinIO客户端"""
        self.client = Minio(
            endpoint=settings.MINIO_ENDPOINT,
            access_key=settings.MINIO_ACCESS_KEY,
            secret_key=settings.MINIO_SECRET_KEY,
            secure=settings.MINIO_SECURE
        )
        self.bucket_name = settings.MINIO_BUCKET_NAME
        self._initialized = False
    
    def _ensure_bucket_exists(self) -> None:
        """确保存储桶存在"""
        try:
            if not self.client.bucket_exists(self.bucket_name):
                self.client.make_bucket(self.bucket_name)
                logger.info("minio_bucket_created", bucket=self.bucket_name)
            else:
                logger.debug("minio_bucket_exists", bucket=self.bucket_name)
            self._initialized = True
        except S3Error as e:
            logger.error("minio_bucket_creation_failed",
                        bucket=self.bucket_name, error=str(e))
            raise

    def initialize(self) -> bool:
        """延迟初始化MinIO连接"""
        if self._initialized:
            return True

        try:
            self._ensure_bucket_exists()
            return True
        except Exception as e:
            logger.error("minio_initialization_failed", error=str(e))
            return False
    
    def check_connection(self) -> bool:
        """检查MinIO连接是否正常"""
        try:
            # 尝试列出存储桶来测试连接
            list(self.client.list_buckets())
            logger.info("minio_connection_check_success")
            return True
        except Exception as e:
            logger.error("minio_connection_check_failed", error=str(e))
            return False
    
    def get_client_info(self) -> Dict[str, Any]:
        """获取MinIO客户端信息"""
        try:
            buckets = list(self.client.list_buckets())
            bucket_info = []
            for bucket in buckets:
                bucket_info.append({
                    "name": bucket.name,
                    "creation_date": bucket.creation_date.isoformat() if bucket.creation_date else None
                })
            
            return {
                "endpoint": settings.MINIO_ENDPOINT,
                "secure": settings.MINIO_SECURE,
                "default_bucket": self.bucket_name,
                "buckets": bucket_info,
                "bucket_exists": self.client.bucket_exists(self.bucket_name)
            }
        except Exception as e:
            logger.error("get_minio_client_info_failed", error=str(e))
            return {"error": str(e)}
    
    def upload_file(
        self, 
        file_data: BinaryIO, 
        object_name: str, 
        content_type: Optional[str] = None,
        metadata: Optional[Dict[str, str]] = None
    ) -> bool:
        """
        上传文件到MinIO
        
        Args:
            file_data: 文件数据流
            object_name: 对象名称（存储路径）
            content_type: 文件MIME类型
            metadata: 文件元数据
            
        Returns:
            是否上传成功
        """
        try:
            # 获取文件大小
            file_data.seek(0, 2)  # 移动到文件末尾
            file_size = file_data.tell()
            file_data.seek(0)  # 重置到文件开头
            
            self.client.put_object(
                bucket_name=self.bucket_name,
                object_name=object_name,
                data=file_data,
                length=file_size,
                content_type=content_type,
                metadata=metadata
            )
            
            logger.info("minio_file_uploaded", 
                       object_name=object_name, 
                       size=file_size,
                       content_type=content_type)
            return True
            
        except S3Error as e:
            logger.error("minio_file_upload_failed", 
                        object_name=object_name, 
                        error=str(e))
            return False
    
    def download_file(self, object_name: str) -> Optional[bytes]:
        """
        从MinIO下载文件
        
        Args:
            object_name: 对象名称（存储路径）
            
        Returns:
            文件数据或None
        """
        try:
            response = self.client.get_object(self.bucket_name, object_name)
            data = response.read()
            response.close()
            response.release_conn()
            
            logger.info("minio_file_downloaded", 
                       object_name=object_name, 
                       size=len(data))
            return data
            
        except S3Error as e:
            logger.error("minio_file_download_failed", 
                        object_name=object_name, 
                        error=str(e))
            return None
    
    def delete_file(self, object_name: str) -> bool:
        """
        删除MinIO中的文件
        
        Args:
            object_name: 对象名称（存储路径）
            
        Returns:
            是否删除成功
        """
        try:
            self.client.remove_object(self.bucket_name, object_name)
            logger.info("minio_file_deleted", object_name=object_name)
            return True
            
        except S3Error as e:
            logger.error("minio_file_delete_failed", 
                        object_name=object_name, 
                        error=str(e))
            return False
    
    def file_exists(self, object_name: str) -> bool:
        """
        检查文件是否存在
        
        Args:
            object_name: 对象名称（存储路径）
            
        Returns:
            文件是否存在
        """
        try:
            self.client.stat_object(self.bucket_name, object_name)
            return True
        except S3Error:
            return False
    
    def get_file_info(self, object_name: str) -> Optional[Dict[str, Any]]:
        """
        获取文件信息
        
        Args:
            object_name: 对象名称（存储路径）
            
        Returns:
            文件信息字典或None
        """
        try:
            stat = self.client.stat_object(self.bucket_name, object_name)
            return {
                "object_name": object_name,
                "size": stat.size,
                "etag": stat.etag,
                "content_type": stat.content_type,
                "last_modified": stat.last_modified.isoformat() if stat.last_modified else None,
                "metadata": stat.metadata
            }
        except S3Error as e:
            logger.error("minio_get_file_info_failed",
                        object_name=object_name,
                        error=str(e))
            return None

    def get_file_data(self, object_name: str) -> Optional[bytes]:
        """
        获取文件数据

        Args:
            object_name: 对象名称（存储路径）

        Returns:
            文件数据或None
        """
        try:
            response = self.client.get_object(self.bucket_name, object_name)
            data = response.read()
            response.close()
            response.release_conn()

            logger.info("minio_file_data_retrieved",
                       object_name=object_name,
                       size=len(data))
            return data

        except S3Error as e:
            logger.error("minio_get_file_data_failed",
                        object_name=object_name,
                        error=str(e))
            return None
        except Exception as e:
            logger.error("minio_get_file_data_error",
                        object_name=object_name,
                        error=str(e))
            return None

    def generate_presigned_put_url(
        self,
        object_name: str,
        expires: timedelta = timedelta(hours=1)
    ) -> Optional[str]:
        """
        生成上传预签名URL

        Args:
            object_name: 对象名称（存储路径）
            expires: URL过期时间，默认1小时

        Returns:
            预签名上传URL或None
        """
        try:
            url = self.client.presigned_put_object(
                bucket_name=self.bucket_name,
                object_name=object_name,
                expires=expires
            )

            logger.info("minio_presigned_put_url_generated",
                       object_name=object_name,
                       expires_in=expires.total_seconds())
            return url

        except S3Error as e:
            logger.error("minio_presigned_put_url_failed",
                        object_name=object_name,
                        error=str(e))
            return None

    def generate_presigned_get_url(
        self,
        object_name: str,
        expires: timedelta = timedelta(hours=1)
    ) -> Optional[str]:
        """
        生成下载预签名URL

        Args:
            object_name: 对象名称（存储路径）
            expires: URL过期时间，默认1小时

        Returns:
            预签名下载URL或None
        """
        try:
            url = self.client.presigned_get_object(
                bucket_name=self.bucket_name,
                object_name=object_name,
                expires=expires
            )

            logger.info("minio_presigned_get_url_generated",
                       object_name=object_name,
                       expires_in=expires.total_seconds())
            return url

        except S3Error as e:
            logger.error("minio_presigned_get_url_failed",
                        object_name=object_name,
                        error=str(e))
            return None

    def ensure_bucket_structure(self) -> bool:
        """
        确保存储桶和基础目录结构存在

        Returns:
            是否成功创建/验证结构
        """
        try:
            # 确保存储桶存在
            if not self.client.bucket_exists(self.bucket_name):
                self.client.make_bucket(self.bucket_name)
                logger.info("minio_bucket_created", bucket_name=self.bucket_name)

            # 创建基础目录结构（通过上传空的标记文件）
            base_directories = [
                "uploads/images/",
                "uploads/videos/",
                "uploads/documents/",
                "uploads/audio/",
                "uploads/other/",
                "processed/images/",
                "processed/videos/",
                "thumbnails/",
                "temp/"
            ]

            for directory in base_directories:
                marker_object = f"{directory}.keep"
                try:
                    # 检查标记文件是否存在
                    self.client.stat_object(self.bucket_name, marker_object)
                except S3Error:
                    # 标记文件不存在，创建它
                    self.client.put_object(
                        bucket_name=self.bucket_name,
                        object_name=marker_object,
                        data=io.BytesIO(b"# Directory structure marker"),
                        length=len(b"# Directory structure marker"),
                        content_type="text/plain"
                    )

            logger.info("minio_bucket_structure_ensured", bucket_name=self.bucket_name)
            return True

        except S3Error as e:
            logger.error("minio_bucket_structure_failed",
                        bucket_name=self.bucket_name,
                        error=str(e))
            return False

    def list_directory(self, prefix: str = "", recursive: bool = False) -> List[Dict[str, Any]]:
        """
        列出目录内容

        Args:
            prefix: 目录前缀
            recursive: 是否递归列出

        Returns:
            文件列表
        """
        try:
            objects = self.client.list_objects(
                bucket_name=self.bucket_name,
                prefix=prefix,
                recursive=recursive
            )

            file_list = []
            for obj in objects:
                # 跳过目录标记文件
                if obj.object_name.endswith('.keep'):
                    continue

                file_info = {
                    "object_name": obj.object_name,
                    "size": obj.size,
                    "last_modified": obj.last_modified.isoformat() if obj.last_modified else None,
                    "etag": obj.etag,
                    "content_type": getattr(obj, 'content_type', None)
                }

                # 解析路径信息
                path_info = StoragePathManager.parse_object_name(obj.object_name)
                file_info.update(path_info)

                file_list.append(file_info)

            logger.info("minio_directory_listed",
                       prefix=prefix,
                       count=len(file_list))
            return file_list

        except S3Error as e:
            logger.error("minio_directory_list_failed",
                        prefix=prefix,
                        error=str(e))
            return []

    def get_storage_statistics(self) -> Dict[str, Any]:
        """
        获取存储统计信息

        Returns:
            存储统计数据
        """
        try:
            stats = {
                "total_files": 0,
                "total_size": 0,
                "by_category": {},
                "by_storage_type": {},
                "by_month": {}
            }

            objects = self.client.list_objects(
                bucket_name=self.bucket_name,
                recursive=True
            )

            for obj in objects:
                # 跳过目录标记文件
                if obj.object_name.endswith('.keep'):
                    continue

                stats["total_files"] += 1
                stats["total_size"] += obj.size

                # 解析路径信息
                path_info = StoragePathManager.parse_object_name(obj.object_name)

                # 按分类统计
                category = path_info.get("category", "other")
                if category not in stats["by_category"]:
                    stats["by_category"][category] = {"count": 0, "size": 0}
                stats["by_category"][category]["count"] += 1
                stats["by_category"][category]["size"] += obj.size

                # 按存储类型统计
                storage_type = path_info.get("storage_type", "unknown")
                if storage_type not in stats["by_storage_type"]:
                    stats["by_storage_type"][storage_type] = {"count": 0, "size": 0}
                stats["by_storage_type"][storage_type]["count"] += 1
                stats["by_storage_type"][storage_type]["size"] += obj.size

                # 按月份统计
                if "year" in path_info and "month" in path_info:
                    month_key = f"{path_info['year']}-{path_info['month']}"
                    if month_key not in stats["by_month"]:
                        stats["by_month"][month_key] = {"count": 0, "size": 0}
                    stats["by_month"][month_key]["count"] += 1
                    stats["by_month"][month_key]["size"] += obj.size

            logger.info("minio_storage_statistics_generated",
                       total_files=stats["total_files"],
                       total_size=stats["total_size"])
            return stats

        except S3Error as e:
            logger.error("minio_storage_statistics_failed", error=str(e))
            return {"error": str(e)}


# 全局MinIO服务实例（延迟初始化）
_minio_service: Optional[MinIOService] = None


def get_minio_service() -> MinIOService:
    """获取MinIO服务实例（延迟初始化）"""
    global _minio_service
    if _minio_service is None:
        _minio_service = MinIOService()
    return _minio_service


# 为了向后兼容，提供直接访问的实例
minio_service = get_minio_service()
