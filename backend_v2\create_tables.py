#!/usr/bin/env python3
"""
直接创建数据库表
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.core.database import engine, Base
from app.models import Task, Batch, File, Result, UserConfig

def create_tables():
    """创建所有表"""
    try:
        print("🔄 正在创建数据库表...")
        
        # 创建所有表
        Base.metadata.create_all(bind=engine)
        
        print("✅ 数据库表创建成功")
        
        # 验证表是否创建成功
        from sqlalchemy import text
        with engine.connect() as conn:
            result = conn.execute(text("SELECT tablename FROM pg_tables WHERE schemaname = 'public'"))
            tables = [row[0] for row in result]
            print(f"📊 创建的表: {tables}")
            
            if 'files' in tables:
                print("✅ files表创建成功")
                return True
            else:
                print("❌ files表创建失败")
                return False
                
    except Exception as e:
        print(f"❌ 创建表失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = create_tables()
    if success:
        print("\n🎉 表创建完成！")
        sys.exit(0)
    else:
        print("\n💥 表创建失败！")
        sys.exit(1)
