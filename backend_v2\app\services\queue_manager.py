"""
队列管理和监控服务
实现任务路由、负载均衡、队列监控和健康检查功能
"""

from typing import Dict, List, Optional, Any
from enum import Enum
import time
from datetime import datetime, timedelta
import structlog
from celery import Celery
from celery.result import AsyncResult
from kombu import Queue, Exchange

from app.core.celery_app import celery_app
from app.models.task import TaskStatus
from app.schemas.task_submit import QueueType

logger = structlog.get_logger()


class QueuePriority(str, Enum):
    """队列优先级枚举"""
    LOW = "low"
    NORMAL = "normal"
    HIGH = "high"
    URGENT = "urgent"


class QueueManager:
    """队列管理器"""
    
    # 队列配置
    QUEUE_CONFIGS = {
        QueueType.CPU_POOL: {
            "max_workers": 4,
            "prefetch_multiplier": 2,
            "max_tasks_per_child": 100,
            "soft_time_limit": 300,  # 5分钟
            "time_limit": 600,       # 10分钟
            "priority_weight": 1.0,
            "description": "CPU密集型任务队列"
        },
        QueueType.GPU_POOL: {
            "max_workers": 2,
            "prefetch_multiplier": 1,
            "max_tasks_per_child": 50,
            "soft_time_limit": 600,  # 10分钟
            "time_limit": 1200,      # 20分钟
            "priority_weight": 2.0,
            "description": "GPU密集型任务队列"
        },
        QueueType.IO_POOL: {
            "max_workers": 8,
            "prefetch_multiplier": 4,
            "max_tasks_per_child": 200,
            "soft_time_limit": 120,  # 2分钟
            "time_limit": 300,       # 5分钟
            "priority_weight": 0.5,
            "description": "IO密集型任务队列"
        },
        QueueType.HYBRID_POOL: {
            "max_workers": 3,
            "prefetch_multiplier": 2,
            "max_tasks_per_child": 75,
            "soft_time_limit": 900,  # 15分钟
            "time_limit": 1800,      # 30分钟
            "priority_weight": 1.5,
            "description": "混合型任务队列"
        }
    }
    
    @classmethod
    def get_queue_config(cls, queue_type: QueueType) -> Dict[str, Any]:
        """获取队列配置"""
        return cls.QUEUE_CONFIGS.get(queue_type, cls.QUEUE_CONFIGS[QueueType.CPU_POOL])
    
    @classmethod
    def get_optimal_queue(cls, task_type: str, file_size: int = 0, priority: QueuePriority = QueuePriority.NORMAL) -> QueueType:
        """
        根据任务类型、文件大小和优先级选择最优队列
        
        Args:
            task_type: 任务类型
            file_size: 文件大小（字节）
            priority: 任务优先级
            
        Returns:
            最优队列类型
        """
        # 基于任务类型的基础路由
        if "fusion" in task_type or "stitching" in task_type or "beauty" in task_type or "texture" in task_type:
            base_queue = QueueType.GPU_POOL
        elif "extract_frame" in task_type or "thumbnail" in task_type:
            base_queue = QueueType.IO_POOL
        elif "video" in task_type and ("resize" in task_type or "transform" in task_type or "edge_detection" in task_type):
            base_queue = QueueType.GPU_POOL
        else:
            base_queue = QueueType.CPU_POOL
        
        # 根据文件大小调整
        if file_size > 100 * 1024 * 1024:  # 100MB以上
            if base_queue == QueueType.CPU_POOL:
                base_queue = QueueType.GPU_POOL
        elif file_size > 500 * 1024 * 1024:  # 500MB以上
            base_queue = QueueType.HYBRID_POOL

        # 根据优先级调整
        if priority == QueuePriority.URGENT:
            # 紧急任务优先使用GPU队列
            if base_queue in [QueueType.CPU_POOL, QueueType.IO_POOL]:
                base_queue = QueueType.GPU_POOL
        
        return base_queue
    
    @classmethod
    def get_queue_status(cls) -> Dict[str, Any]:
        """获取所有队列状态"""
        try:
            # 获取Celery inspect对象
            inspect = celery_app.control.inspect()
            
            # 获取活跃任务
            active_tasks = inspect.active() or {}
            
            # 获取预定任务
            scheduled_tasks = inspect.scheduled() or {}
            
            # 获取保留任务
            reserved_tasks = inspect.reserved() or {}
            
            # 获取统计信息
            stats = inspect.stats() or {}
            
            queue_status = {}
            
            for queue_type in QueueType:
                queue_name = queue_type.value
                config = cls.get_queue_config(queue_type)
                
                # 计算队列中的任务数量
                active_count = sum(len(tasks) for worker, tasks in active_tasks.items() 
                                 if any(task.get('delivery_info', {}).get('routing_key') == queue_name 
                                       for task in tasks))
                
                scheduled_count = sum(len(tasks) for worker, tasks in scheduled_tasks.items()
                                    if any(task.get('delivery_info', {}).get('routing_key') == queue_name 
                                          for task in tasks))
                
                reserved_count = sum(len(tasks) for worker, tasks in reserved_tasks.items()
                                   if any(task.get('delivery_info', {}).get('routing_key') == queue_name 
                                         for task in tasks))
                
                # 计算负载率
                total_tasks = active_count + scheduled_count + reserved_count
                max_workers = config["max_workers"]
                load_percentage = (total_tasks / max_workers * 100) if max_workers > 0 else 0
                
                queue_status[queue_name] = {
                    "config": config,
                    "active_tasks": active_count,
                    "scheduled_tasks": scheduled_count,
                    "reserved_tasks": reserved_count,
                    "total_tasks": total_tasks,
                    "max_workers": max_workers,
                    "load_percentage": min(load_percentage, 100),
                    "status": "healthy" if load_percentage < 80 else "busy" if load_percentage < 95 else "overloaded",
                    "last_updated": datetime.utcnow().isoformat()
                }
            
            return {
                "queues": queue_status,
                "overall_status": "healthy",
                "timestamp": datetime.utcnow().isoformat()
            }
            
        except Exception as e:
            logger.error("get_queue_status_error", error=str(e))
            return {
                "queues": {},
                "overall_status": "error",
                "error": str(e),
                "timestamp": datetime.utcnow().isoformat()
            }
    
    @classmethod
    def get_worker_status(cls) -> Dict[str, Any]:
        """获取工作进程状态"""
        try:
            inspect = celery_app.control.inspect()
            
            # 获取活跃的工作进程
            active_workers = inspect.active_queues() or {}
            
            # 获取工作进程统计
            stats = inspect.stats() or {}
            
            worker_status = {}
            
            for worker_name, worker_stats in stats.items():
                worker_status[worker_name] = {
                    "status": "online",
                    "queues": list(active_workers.get(worker_name, {}).keys()) if worker_name in active_workers else [],
                    "pool": worker_stats.get("pool", {}),
                    "rusage": worker_stats.get("rusage", {}),
                    "clock": worker_stats.get("clock", 0),
                    "pid": worker_stats.get("pid", 0),
                    "last_heartbeat": datetime.utcnow().isoformat()
                }
            
            return {
                "workers": worker_status,
                "total_workers": len(worker_status),
                "online_workers": len([w for w in worker_status.values() if w["status"] == "online"]),
                "timestamp": datetime.utcnow().isoformat()
            }
            
        except Exception as e:
            logger.error("get_worker_status_error", error=str(e))
            return {
                "workers": {},
                "total_workers": 0,
                "online_workers": 0,
                "error": str(e),
                "timestamp": datetime.utcnow().isoformat()
            }


class CeleryTaskController:
    """Celery任务控制器"""
    
    @classmethod
    def pause_celery_task(cls, celery_job_id: str) -> bool:
        """
        暂停Celery任务
        
        Args:
            celery_job_id: Celery任务ID
            
        Returns:
            是否成功暂停
        """
        try:
            # 获取任务结果对象
            result = AsyncResult(celery_job_id, app=celery_app)
            
            if result.state in ['PENDING', 'STARTED', 'RETRY']:
                # 撤销任务（相当于暂停）
                celery_app.control.revoke(celery_job_id, terminate=False)
                logger.info("celery_task_paused", job_id=celery_job_id, state=result.state)
                return True
            else:
                logger.warning("celery_task_pause_failed", job_id=celery_job_id, state=result.state, reason="task_not_pausable")
                return False
                
        except Exception as e:
            logger.error("pause_celery_task_error", job_id=celery_job_id, error=str(e))
            return False
    
    @classmethod
    def resume_celery_task(cls, celery_job_id: str, task_data: Dict[str, Any]) -> bool:
        """
        恢复Celery任务（重新提交）
        
        Args:
            celery_job_id: 原Celery任务ID
            task_data: 任务数据
            
        Returns:
            是否成功恢复
        """
        try:
            # 注意：Celery本身不支持真正的暂停/恢复
            # 这里的实现是重新提交任务
            logger.info("celery_task_resume_attempt", job_id=celery_job_id)
            
            # 实际的恢复需要重新提交任务，这需要在TaskDispatcher中实现
            # 这里只是记录日志，实际恢复逻辑在API层处理
            return True
            
        except Exception as e:
            logger.error("resume_celery_task_error", job_id=celery_job_id, error=str(e))
            return False
    
    @classmethod
    def cancel_celery_task(cls, celery_job_id: str) -> bool:
        """
        取消Celery任务
        
        Args:
            celery_job_id: Celery任务ID
            
        Returns:
            是否成功取消
        """
        try:
            # 获取任务结果对象
            result = AsyncResult(celery_job_id, app=celery_app)
            
            if result.state not in ['SUCCESS', 'FAILURE', 'REVOKED']:
                # 撤销并终止任务
                celery_app.control.revoke(celery_job_id, terminate=True)
                logger.info("celery_task_cancelled", job_id=celery_job_id, state=result.state)
                return True
            else:
                logger.warning("celery_task_cancel_failed", job_id=celery_job_id, state=result.state, reason="task_already_finished")
                return False
                
        except Exception as e:
            logger.error("cancel_celery_task_error", job_id=celery_job_id, error=str(e))
            return False
    
    @classmethod
    def get_task_status(cls, celery_job_id: str) -> Dict[str, Any]:
        """
        获取Celery任务状态
        
        Args:
            celery_job_id: Celery任务ID
            
        Returns:
            任务状态信息
        """
        try:
            result = AsyncResult(celery_job_id, app=celery_app)
            
            return {
                "job_id": celery_job_id,
                "state": result.state,
                "result": result.result if result.successful() else None,
                "traceback": result.traceback if result.failed() else None,
                "info": result.info,
                "successful": result.successful(),
                "failed": result.failed(),
                "ready": result.ready(),
                "timestamp": datetime.utcnow().isoformat()
            }
            
        except Exception as e:
            logger.error("get_celery_task_status_error", job_id=celery_job_id, error=str(e))
            return {
                "job_id": celery_job_id,
                "state": "ERROR",
                "error": str(e),
                "timestamp": datetime.utcnow().isoformat()
            }
