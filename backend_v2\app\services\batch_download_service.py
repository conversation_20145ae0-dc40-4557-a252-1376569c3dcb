"""
批量下载服务
负责文件的批量下载、ZIP打包和权限验证
"""
import os
import zipfile
import io
from typing import List, Dict, Any, Optional, Generator, Tu<PERSON>
from datetime import datetime, timedelta
from sqlalchemy.orm import Session

from app.models.file import File, FileStatus
from app.models.task import Task, TaskStatus
from app.models.batch import Batch, BatchStatus
from app.crud.crud_file import file as file_crud
from app.crud.crud_task import task as task_crud
from app.crud.crud_batch import batch as batch_crud
from app.services.minio_service import MinIOService
from app.services.file_usage_service import FileUsageService


class BatchDownloadService:
    """批量下载服务"""
    
    @staticmethod
    def validate_download_permission(
        db: Session,
        file_ids: List[int],
        user_id: Optional[str] = None
    ) -> Tuple[bool, str, List[File]]:
        """
        验证下载权限
        
        Args:
            db: 数据库会话
            file_ids: 文件ID列表
            user_id: 用户ID（可选，当前项目无用户认证）
            
        Returns:
            (是否有权限, 错误信息, 有效文件列表)
        """
        try:
            valid_files = []
            
            for file_id in file_ids:
                file_obj = file_crud.get(db=db, id=file_id)
                
                if not file_obj:
                    return False, f"File {file_id} not found", []
                
                # 检查文件状态
                if file_obj.status == FileStatus.DELETED:
                    return False, f"File {file_id} has been deleted", []
                
                if file_obj.status != FileStatus.UPLOADED:
                    return False, f"File {file_id} is not available for download", []
                
                # 在有用户认证的系统中，这里可以添加权限检查
                # 当前项目是个人项目，跳过用户权限验证
                
                valid_files.append(file_obj)
            
            return True, "", valid_files
            
        except Exception as e:
            return False, f"Permission validation failed: {str(e)}", []
    
    @staticmethod
    def validate_result_download_permission(
        db: Session,
        result_id: str,
        user_id: Optional[str] = None
    ) -> Tuple[bool, str, List[File]]:
        """
        验证结果文件下载权限
        
        Args:
            db: 数据库会话
            result_id: 结果ID（可以是task_id或batch_id）
            user_id: 用户ID（可选）
            
        Returns:
            (是否有权限, 错误信息, 结果文件列表)
        """
        try:
            result_files = []
            
            # 首先尝试作为task_id查询
            if result_id.isdigit():
                task_obj = task_crud.get(db=db, id=int(result_id))
                if task_obj:
                    # 查询任务的结果文件
                    task_files = file_crud.get_by_task_id(db=db, task_id=task_obj.id)
                    result_files.extend([f for f in task_files if f.status == FileStatus.UPLOADED])
            
            # 如果没有找到任务，尝试作为batch_id查询
            if not result_files and result_id.isdigit():
                batch_obj = batch_crud.get(db=db, id=int(result_id))
                if batch_obj:
                    # 查询批次的结果文件
                    batch_files = file_crud.get_by_batch_id(db=db, batch_id=batch_obj.id)
                    result_files.extend([f for f in batch_files if f.status == FileStatus.UPLOADED])
            
            if not result_files:
                return False, f"No files found for result {result_id}", []
            
            return True, "", result_files
            
        except Exception as e:
            return False, f"Result validation failed: {str(e)}", []
    
    @staticmethod
    def create_streaming_zip(
        files: List[File],
        minio_service: MinIOService,
        chunk_size: int = 8192
    ) -> Generator[bytes, None, None]:
        """
        创建流式ZIP文件
        
        Args:
            files: 文件列表
            minio_service: MinIO服务
            chunk_size: 数据块大小
            
        Yields:
            ZIP文件数据块
        """
        # 创建内存中的ZIP文件
        zip_buffer = io.BytesIO()
        
        with zipfile.ZipFile(zip_buffer, 'w', zipfile.ZIP_DEFLATED) as zip_file:
            for file_obj in files:
                try:
                    # 从MinIO获取文件数据
                    file_data = minio_service.get_file_data(file_obj.storage_path)
                    
                    if file_data:
                        # 生成ZIP内的文件名（避免路径冲突）
                        zip_filename = f"{file_obj.id}_{file_obj.filename}"
                        
                        # 添加文件到ZIP
                        zip_file.writestr(zip_filename, file_data)
                    
                except Exception as e:
                    # 如果单个文件失败，添加错误信息文件
                    error_filename = f"ERROR_{file_obj.id}_{file_obj.filename}.txt"
                    error_content = f"Failed to download file: {str(e)}"
                    zip_file.writestr(error_filename, error_content.encode('utf-8'))
        
        # 获取ZIP数据
        zip_buffer.seek(0)
        zip_data = zip_buffer.getvalue()
        
        # 分块返回数据
        for i in range(0, len(zip_data), chunk_size):
            yield zip_data[i:i + chunk_size]
    
    @staticmethod
    def create_large_streaming_zip(
        files: List[File],
        minio_service: MinIOService,
        chunk_size: int = 8192
    ) -> Generator[bytes, None, None]:
        """
        创建大文件流式ZIP（避免内存问题）
        
        Args:
            files: 文件列表
            minio_service: MinIO服务
            chunk_size: 数据块大小
            
        Yields:
            ZIP文件数据块
        """
        class StreamingZipFile:
            def __init__(self):
                self.buffer = io.BytesIO()
                self.zip_file = zipfile.ZipFile(self.buffer, 'w', zipfile.ZIP_DEFLATED)
                self.position = 0
            
            def add_file(self, filename: str, file_data: bytes):
                self.zip_file.writestr(filename, file_data)
                
                # 检查缓冲区大小，如果超过阈值就输出
                current_size = self.buffer.tell()
                if current_size - self.position > chunk_size * 10:  # 10个chunk的大小
                    yield self.get_chunk()
            
            def get_chunk(self):
                current_pos = self.buffer.tell()
                self.buffer.seek(self.position)
                chunk = self.buffer.read(current_pos - self.position)
                self.position = current_pos
                return chunk
            
            def finalize(self):
                self.zip_file.close()
                self.buffer.seek(self.position)
                remaining = self.buffer.read()
                return remaining
        
        streaming_zip = StreamingZipFile()
        
        try:
            for file_obj in files:
                try:
                    # 从MinIO获取文件数据
                    file_data = minio_service.get_file_data(file_obj.storage_path)
                    
                    if file_data:
                        # 生成ZIP内的文件名
                        zip_filename = f"{file_obj.id}_{file_obj.filename}"
                        
                        # 添加文件到ZIP并可能产生数据块
                        yield from streaming_zip.add_file(zip_filename, file_data)
                    
                except Exception as e:
                    # 添加错误信息文件
                    error_filename = f"ERROR_{file_obj.id}_{file_obj.filename}.txt"
                    error_content = f"Failed to download file: {str(e)}"
                    yield from streaming_zip.add_file(error_filename, error_content.encode('utf-8'))
            
            # 输出最后的数据
            final_chunk = streaming_zip.finalize()
            if final_chunk:
                yield final_chunk
                
        except Exception as e:
            # 如果整个过程失败，返回错误ZIP
            error_zip = io.BytesIO()
            with zipfile.ZipFile(error_zip, 'w') as zf:
                zf.writestr("ERROR.txt", f"Batch download failed: {str(e)}".encode('utf-8'))
            error_zip.seek(0)
            yield error_zip.getvalue()
    
    @staticmethod
    def record_batch_download(
        db: Session,
        file_ids: List[int],
        download_type: str = "batch_zip"
    ) -> bool:
        """
        记录批量下载统计
        
        Args:
            db: 数据库会话
            file_ids: 文件ID列表
            download_type: 下载类型
            
        Returns:
            是否记录成功
        """
        try:
            for file_id in file_ids:
                FileUsageService.record_file_access(
                    db=db,
                    file_id=file_id,
                    access_type=download_type
                )
            return True
        except Exception:
            return False
    
    @staticmethod
    def get_download_statistics(
        db: Session,
        file_ids: List[int]
    ) -> Dict[str, Any]:
        """
        获取下载统计信息
        
        Args:
            db: 数据库会话
            file_ids: 文件ID列表
            
        Returns:
            统计信息
        """
        try:
            total_size = 0
            file_count = len(file_ids)
            file_types = {}
            
            for file_id in file_ids:
                file_obj = file_crud.get(db=db, id=file_id)
                if file_obj:
                    total_size += file_obj.file_size or 0
                    file_type = file_obj.file_type.value if file_obj.file_type else 'unknown'
                    file_types[file_type] = file_types.get(file_type, 0) + 1
            
            return {
                "file_count": file_count,
                "total_size_bytes": total_size,
                "total_size_mb": round(total_size / (1024 * 1024), 2),
                "file_types": file_types,
                "estimated_zip_size_mb": round(total_size * 0.8 / (1024 * 1024), 2),  # 估算压缩后大小
                "generated_at": datetime.now().isoformat()
            }
            
        except Exception:
            return {
                "file_count": 0,
                "total_size_bytes": 0,
                "total_size_mb": 0,
                "file_types": {},
                "estimated_zip_size_mb": 0,
                "generated_at": datetime.now().isoformat()
            }
