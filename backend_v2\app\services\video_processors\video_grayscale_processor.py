"""
视频灰度转换处理器
实现视频的灰度转换功能，作为VideoProcessorBase的第一个具体实现
"""
import os
import tempfile
from typing import Dict, Any, Optional
import cv2
import numpy as np
import structlog
from sqlalchemy.orm import Session

from app.services.video_processor_base import VideoProcessorBase
from app.services.minio_service import MinIOService

logger = structlog.get_logger()


class VideoGrayscaleProcessor(VideoProcessorBase):
    """视频灰度转换处理器"""
    
    def process_image(self, image: np.ndarray, parameters: Dict[str, Any]) -> np.ndarray:
        """
        处理单帧图像（灰度转换）
        
        Args:
            image: 输入图像帧（BGR格式）
            parameters: 处理参数
                
        Returns:
            灰度转换后的图像帧（3通道灰度图）
        """
        try:
            # 转换为灰度图
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            
            # 转换回3通道格式以保持视频格式一致性
            gray_3ch = cv2.cvtColor(gray, cv2.COLOR_GRAY2BGR)
            
            return gray_3ch
            
        except Exception as e:
            logger.error("frame_grayscale_processing_failed", error=str(e))
            raise ValueError(f"Frame grayscale processing failed: {str(e)}")
    
    def process_frame(
        self, 
        frame: np.ndarray, 
        parameters: Dict[str, Any], 
        frame_index: int
    ) -> np.ndarray:
        """
        处理单帧（重写以添加帧索引日志）
        
        Args:
            frame: 输入帧
            parameters: 处理参数
            frame_index: 帧索引
            
        Returns:
            处理后的帧
        """
        # 每100帧记录一次日志
        if frame_index % 100 == 0:
            logger.debug("processing_frame", frame_index=frame_index)
        
        return self.process_image(frame, parameters)
    
    def process_video(
        self, 
        video_path: str, 
        parameters: Dict[str, Any], 
        video_info: Dict[str, Any],
        task_id: Optional[str] = None
    ) -> str:
        """
        处理整个视频文件
        
        Args:
            video_path: 输入视频路径
            parameters: 处理参数
            video_info: 视频信息
            task_id: 任务ID
            
        Returns:
            处理后的视频文件路径
        """
        try:
            # 验证和转换参数
            is_valid, error_msg, validated_params = self.validate_and_convert_parameters(
                "video_grayscale", parameters
            )
            if not is_valid:
                raise ValueError(f"Parameter validation failed: {error_msg}")

            # 分离视频处理参数和性能参数
            video_params, performance_params = self.extract_processing_parameters(validated_params)

            logger.info("processing_video_grayscale",
                       video_path=video_path,
                       video_info=video_info,
                       validated_params=validated_params)
            
            # 创建输出临时文件
            temp_fd, output_path = tempfile.mkstemp(suffix="_grayscale.mp4")
            self.temp_files.append(output_path)
            os.close(temp_fd)  # 关闭文件描述符
            
            # 打开输入视频
            cap = cv2.VideoCapture(video_path)
            if not cap.isOpened():
                raise ValueError(f"Cannot open video file: {video_path}")
            
            try:
                # 获取视频属性
                fps = cap.get(cv2.CAP_PROP_FPS)
                width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
                height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
                total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
                
                # 定义编码器和创建VideoWriter
                fourcc = cv2.VideoWriter_fourcc(*'mp4v')
                out = cv2.VideoWriter(output_path, fourcc, fps, (width, height))
                
                if not out.isOpened():
                    raise ValueError("Failed to create output video writer")
                
                try:
                    frame_index = 0
                    processed_frames = 0
                    
                    # 逐帧处理
                    while True:
                        ret, frame = cap.read()
                        if not ret:
                            break
                        
                        # 处理帧
                        processed_frame = self.process_frame(frame, parameters, frame_index)
                        
                        # 写入输出视频
                        out.write(processed_frame)
                        
                        processed_frames += 1
                        frame_index += 1
                        
                        # 更新进度
                        if task_id and frame_index % 30 == 0:  # 每30帧更新一次进度
                            progress = 15 + int((frame_index / total_frames) * 70)  # 15-85%
                            self.update_task_progress(
                                task_id, 
                                progress, 
                                100, 
                                f"Processing frame {frame_index}/{total_frames}"
                            )
                    
                    logger.info("video_grayscale_processing_completed",
                               total_frames=total_frames,
                               processed_frames=processed_frames,
                               output_path=output_path)
                    
                    return output_path
                    
                finally:
                    out.release()
                    
            finally:
                cap.release()
                
        except Exception as e:
            logger.error("video_grayscale_processing_failed", 
                        video_path=video_path,
                        error=str(e))
            raise ValueError(f"Video grayscale processing failed: {str(e)}")


def create_video_grayscale_processor(minio_service: MinIOService, db_session: Session) -> VideoGrayscaleProcessor:
    """
    创建视频灰度转换处理器实例
    
    Args:
        minio_service: MinIO服务实例
        db_session: 数据库会话
        
    Returns:
        视频灰度转换处理器实例
    """
    return VideoGrayscaleProcessor(minio_service, db_session)


def validate_video_grayscale_parameters(parameters: Dict[str, Any]) -> Dict[str, Any]:
    """
    验证和转换视频灰度转换处理参数
    
    Args:
        parameters: 输入参数
        
    Returns:
        验证后的参数
        
    Raises:
        ValueError: 参数无效时抛出
    """
    try:
        # 视频灰度转换不需要特殊参数，但保持接口一致性
        validated_params = VideoParameterConverter.convert_legacy_video_parameters(
            "video_grayscale", 
            parameters
        )
        
        # 验证参数
        is_valid, error_msg = VideoParameterConverter.validate_video_parameters(
            "video_grayscale", 
            validated_params
        )
        
        if not is_valid:
            raise ValueError(error_msg)
        
        return validated_params
        
    except Exception as e:
        logger.error("video_grayscale_parameter_validation_failed", 
                    parameters=parameters,
                    error=str(e))
        raise ValueError(f"Parameter validation failed: {str(e)}")


# 为了向后兼容，提供旧格式的函数接口
def video_grayscale_legacy(video_path: str) -> str:
    """
    旧格式的视频灰度转换函数接口（向后兼容）
    
    Args:
        video_path: 视频文件路径
        
    Returns:
        处理后的视频路径
        
    Note:
        这个函数仅用于向后兼容，新代码应使用VideoGrayscaleProcessor
    """
    logger.warning("using_legacy_video_grayscale_interface", 
                  video_path=video_path)
    
    # 这里可以实现向旧接口的适配逻辑
    # 但在新架构中，建议直接使用VideoGrayscaleProcessor
    raise NotImplementedError("Legacy interface not implemented in new architecture. Use VideoGrayscaleProcessor instead.")
