"""
图像锐化处理器
实现图像锐化功能，增强图像的边缘和细节
"""
from typing import Dict, Any
import cv2
import numpy as np
import structlog

from app.services.image_processor_base import ImageProcessorBase, ParameterConverter

logger = structlog.get_logger()


class SharpenProcessor(ImageProcessorBase):
    """图像锐化处理器"""
    
    def process_image(self, image: np.ndarray, parameters: Dict[str, Any]) -> np.ndarray:
        """
        对图像进行锐化处理
        
        Args:
            image: 输入图像（BGR格式）
            parameters: 处理参数
                - intensity: 锐化强度，范围0.1-5.0，默认1.0
                
        Returns:
            锐化后的图像
        """
        try:
            intensity = parameters.get('intensity', 1.0)
            
            logger.info("processing_image_sharpen", 
                       input_shape=image.shape,
                       intensity=intensity)
            
            # 创建锐化核
            # 基础锐化核：中心为正值，周围为负值
            kernel = np.array([
                [-1, -1, -1],
                [-1,  9, -1],
                [-1, -1, -1]
            ], dtype=np.float32)
            
            # 根据强度调整核
            # 强度越高，锐化效果越明显
            center_value = 1 + 8 * intensity
            edge_value = -intensity
            
            adjusted_kernel = np.array([
                [edge_value, edge_value, edge_value],
                [edge_value, center_value, edge_value],
                [edge_value, edge_value, edge_value]
            ], dtype=np.float32)
            
            # 应用锐化滤波器
            sharpened = cv2.filter2D(image, -1, adjusted_kernel)
            
            # 确保像素值在有效范围内
            sharpened = np.clip(sharpened, 0, 255).astype(np.uint8)
            
            logger.info("image_sharpen_completed",
                       output_shape=sharpened.shape,
                       intensity=intensity)
            
            return sharpened
            
        except Exception as e:
            logger.error("image_sharpen_failed", 
                        intensity=parameters.get('intensity'),
                        error=str(e))
            raise ValueError(f"Image sharpening failed: {str(e)}")


def create_sharpen_processor(minio_service, db_session) -> SharpenProcessor:
    """
    创建锐化处理器实例
    
    Args:
        minio_service: MinIO服务实例
        db_session: 数据库会话
        
    Returns:
        锐化处理器实例
    """
    return SharpenProcessor(minio_service, db_session)


def validate_sharpen_parameters(parameters: Dict[str, Any]) -> Dict[str, Any]:
    """
    验证和转换锐化处理参数
    
    Args:
        parameters: 输入参数
        
    Returns:
        验证后的参数
        
    Raises:
        ValueError: 参数无效时抛出
    """
    try:
        # 转换旧格式参数
        converted_params = ParameterConverter.convert_legacy_parameters("sharpen", parameters)
        
        # 验证参数
        is_valid, error_msg = ParameterConverter.validate_parameters("sharpen", converted_params)
        if not is_valid:
            raise ValueError(error_msg)
        
        # 确保intensity参数存在且在合理范围内
        intensity = converted_params.get('intensity', 1.0)
        
        # 类型转换和范围检查
        try:
            intensity = float(intensity)
        except (ValueError, TypeError):
            raise ValueError("Intensity must be a number")
        
        if intensity < 0.1 or intensity > 5.0:
            raise ValueError("Intensity must be between 0.1 and 5.0")
        
        return {
            'intensity': intensity
        }
        
    except Exception as e:
        logger.error("sharpen_parameter_validation_failed", 
                    parameters=parameters,
                    error=str(e))
        raise ValueError(f"Parameter validation failed: {str(e)}")


# 为了向后兼容，提供旧格式的函数接口
def sharpen_image_legacy(image_path: str, intensity: float = 1.0) -> str:
    """
    旧格式的锐化函数接口（向后兼容）
    
    Args:
        image_path: 图像文件路径
        intensity: 锐化强度
        
    Returns:
        处理后的图像路径
        
    Note:
        这个函数仅用于向后兼容，新代码应使用SharpenProcessor
    """
    logger.warning("using_legacy_sharpen_interface", 
                  image_path=image_path,
                  intensity=intensity)
    
    # 这里可以实现向旧接口的适配逻辑
    # 但在新架构中，建议直接使用SharpenProcessor
    raise NotImplementedError("Legacy interface not implemented in new architecture. Use SharpenProcessor instead.")
