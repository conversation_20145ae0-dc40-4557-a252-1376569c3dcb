#!/usr/bin/env python3
"""
测试文件使用统计功能
"""
import sys
import os
import io
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from fastapi.testclient import TestClient
from app.main import app
from app.core.database import get_db_session
from app.services.file_usage_service import FileUsageService

def test_file_usage_recording():
    """测试文件使用记录功能"""
    print("🧪 测试文件使用记录功能...")
    
    try:
        client = TestClient(app)
        db = get_db_session()
        
        # 先上传一个文件
        test_file_content = b"This is a test file for usage tracking"
        test_file = io.BytesIO(test_file_content)
        
        files = {
            "file": ("usage_test.jpg", test_file, "image/jpeg")
        }
        
        print("🔄 上传测试文件...")
        upload_response = client.post("/api/files/upload", files=files)
        
        if upload_response.status_code != 200:
            print(f"❌ 文件上传失败: {upload_response.status_code}")
            return False
        
        result = upload_response.json()
        file_db_id = result.get('database_id')
        print(f"✅ 文件上传成功，数据库ID: {file_db_id}")
        
        # 测试手动记录使用统计
        print("🔄 记录文件访问...")
        success = FileUsageService.record_file_access(db=db, file_id=file_db_id, access_type="view")
        if success:
            print("✅ 文件访问记录成功")
        else:
            print("❌ 文件访问记录失败")
            return False
        
        # 再记录几次不同类型的访问
        FileUsageService.record_file_access(db=db, file_id=file_db_id, access_type="download")
        FileUsageService.record_file_access(db=db, file_id=file_db_id, access_type="preview")
        FileUsageService.record_file_access(db=db, file_id=file_db_id, access_type="view")
        
        # 获取使用统计
        print("🔄 获取文件使用统计...")
        stats = FileUsageService.get_file_usage_stats(db=db, file_id=file_db_id)
        
        if stats:
            print(f"✅ 使用统计获取成功!")
            print(f"📊 总访问次数: {stats.get('total_accesses', 0)}")
            print(f"📂 访问类型分布: {stats.get('access_types', {})}")
            print(f"🕐 首次访问: {stats.get('first_accessed', 'N/A')}")
            print(f"🕐 最后访问: {stats.get('last_accessed', 'N/A')}")
            
            access_frequency = stats.get('access_frequency', {})
            if access_frequency:
                print(f"📈 访问频率: {access_frequency.get('accesses_per_day', 0):.2f} 次/天")
            
            return stats.get('total_accesses', 0) >= 4  # 应该有4次访问
        else:
            print("❌ 使用统计获取失败")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        if 'db' in locals():
            db.close()

def test_usage_apis():
    """测试使用统计API"""
    print("\n🧪 测试使用统计API...")
    
    try:
        client = TestClient(app)
        
        # 测试使用摘要API
        print("🔄 获取使用摘要...")
        summary_response = client.get("/api/files/usage/summary")
        
        print(f"📡 摘要响应状态码: {summary_response.status_code}")
        
        if summary_response.status_code == 200:
            summary = summary_response.json()
            print(f"✅ 使用摘要获取成功!")
            print(f"📊 总文件数: {summary.get('total_files', 0)}")
            print(f"📊 已访问文件: {summary.get('accessed_files', 0)}")
            print(f"📊 从未访问: {summary.get('never_accessed', 0)}")
            print(f"📊 访问率: {summary.get('access_rate', 0):.1f}%")
            print(f"📊 总访问次数: {summary.get('total_accesses', 0)}")
            print(f"📊 平均访问次数: {summary.get('average_accesses_per_file', 0):.1f}")
            
            access_types = summary.get('access_types_summary', {})
            if access_types:
                print(f"📂 访问类型分布: {access_types}")
        else:
            print(f"❌ 使用摘要获取失败: {summary_response.status_code}")
            return False
        
        # 测试热门文件API
        print("\n🔄 获取热门文件...")
        popular_response = client.get("/api/files/usage/popular?limit=5&days=30")
        
        print(f"📡 热门文件响应状态码: {popular_response.status_code}")
        
        if popular_response.status_code == 200:
            popular_result = popular_response.json()
            print(f"✅ 热门文件获取成功!")
            print(f"🔥 发现 {popular_result.get('total_found', 0)} 个热门文件")
            
            popular_files = popular_result.get('popular_files', [])
            for i, file_info in enumerate(popular_files[:3], 1):  # 只显示前3个
                print(f"   {i}. 文件ID: {file_info.get('id')}")
                print(f"      文件名: {file_info.get('filename')}")
                print(f"      总访问: {file_info.get('total_accesses')} 次")
                print(f"      近期访问: {file_info.get('recent_accesses')} 次")
        else:
            print(f"❌ 热门文件获取失败: {popular_response.status_code}")
            return False
        
        # 测试未使用文件API
        print("\n🔄 获取未使用文件...")
        unused_response = client.get("/api/files/usage/unused?days=90&limit=5")
        
        print(f"📡 未使用文件响应状态码: {unused_response.status_code}")
        
        if unused_response.status_code == 200:
            unused_result = unused_response.json()
            print(f"✅ 未使用文件获取成功!")
            print(f"🗑️ 发现 {unused_result.get('total_found', 0)} 个未使用文件")
            
            unused_files = unused_result.get('unused_files', [])
            for i, file_info in enumerate(unused_files[:3], 1):  # 只显示前3个
                print(f"   {i}. 文件ID: {file_info.get('id')}")
                print(f"      文件名: {file_info.get('filename')}")
                print(f"      未使用天数: {file_info.get('days_unused')} 天")
                print(f"      总访问: {file_info.get('total_accesses')} 次")
        else:
            print(f"❌ 未使用文件获取失败: {unused_response.status_code}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        return False

def test_download_with_usage_tracking():
    """测试下载时的使用统计记录"""
    print("\n🧪 测试下载时的使用统计记录...")
    
    try:
        client = TestClient(app)
        
        # 先上传一个文件
        test_file_content = b"This is a test file for download tracking"
        test_file = io.BytesIO(test_file_content)
        
        files = {
            "file": ("download_test.jpg", test_file, "image/jpeg")
        }
        
        print("🔄 上传测试文件...")
        upload_response = client.post("/api/files/upload", files=files)
        
        if upload_response.status_code != 200:
            print(f"❌ 文件上传失败: {upload_response.status_code}")
            return False
        
        result = upload_response.json()
        file_db_id = result.get('database_id')
        print(f"✅ 文件上传成功，数据库ID: {file_db_id}")
        
        # 尝试下载文件（会触发使用统计记录）
        print("🔄 下载文件...")
        download_response = client.get(f"/api/files/{file_db_id}/download", follow_redirects=False)
        
        print(f"📡 下载响应状态码: {download_response.status_code}")
        
        if download_response.status_code == 302:
            print("✅ 下载重定向成功（302）")
            
            # 检查使用统计是否被记录
            print("🔄 检查使用统计...")
            stats_response = client.get(f"/api/files/{file_db_id}/usage")
            
            if stats_response.status_code == 200:
                stats = stats_response.json()
                download_count = stats.get('access_types', {}).get('download', 0)
                print(f"✅ 使用统计检查成功!")
                print(f"📊 下载次数: {download_count}")
                
                return download_count > 0
            else:
                print(f"❌ 使用统计检查失败: {stats_response.status_code}")
                return False
        else:
            print(f"❌ 下载失败: {download_response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        return False

if __name__ == "__main__":
    success1 = test_file_usage_recording()
    success2 = test_usage_apis()
    success3 = test_download_with_usage_tracking()
    
    if success1 and success2 and success3:
        print("\n🎉 所有文件使用统计测试通过！")
        sys.exit(0)
    else:
        print("\n💥 文件使用统计测试失败！")
        sys.exit(1)
