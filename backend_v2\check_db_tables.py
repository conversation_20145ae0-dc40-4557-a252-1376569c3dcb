#!/usr/bin/env python3
"""
检查数据库表
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.core.database import engine
from sqlalchemy import text

def check_tables():
    """检查数据库中的表"""
    try:
        with engine.connect() as conn:
            # 检查所有表
            result = conn.execute(text("SELECT tablename FROM pg_tables WHERE schemaname = 'public'"))
            tables = [row[0] for row in result]
            print(f"📊 数据库中的表: {tables}")
            
            if not tables:
                print("❌ 数据库中没有表，需要运行迁移")
                return False
            
            # 检查是否有files表
            if 'files' in tables:
                print("✅ files表存在")
                
                # 检查files表结构
                result = conn.execute(text("""
                    SELECT column_name, data_type 
                    FROM information_schema.columns 
                    WHERE table_name = 'files' AND table_schema = 'public'
                    ORDER BY ordinal_position
                """))
                columns = [(row[0], row[1]) for row in result]
                print(f"📋 files表字段: {columns}")
                
                return True
            else:
                print("❌ files表不存在")
                return False
                
    except Exception as e:
        print(f"❌ 检查数据库失败: {str(e)}")
        return False

if __name__ == "__main__":
    success = check_tables()
    if success:
        print("\n✅ 数据库检查通过")
    else:
        print("\n❌ 数据库检查失败")
