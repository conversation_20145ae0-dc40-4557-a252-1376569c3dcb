"""
图像处理器模块
包含各种图像处理算法的实现
"""

from .grayscale_processor import GrayscaleProcessor, create_grayscale_processor, validate_grayscale_parameters
from .sharpen_processor import SharpenProcessor, create_sharpen_processor, validate_sharpen_parameters
from .edge_detection_processor import EdgeDetectionProcessor, create_edge_detection_processor, validate_edge_detection_parameters
from .gamma_correction_processor import GammaCorrectionProcessor, create_gamma_correction_processor, validate_gamma_correction_parameters
from .image_fusion_processor import ImageFusionProcessor, create_image_fusion_processor, validate_image_fusion_parameters
from .beauty_enhancement_processor import BeautyEnhancementProcessor, create_beauty_enhancement_processor, validate_beauty_enhancement_parameters
from .image_stitching_processor import ImageStitchingProcessor, create_image_stitching_processor, validate_image_stitching_parameters
from .texture_transfer_processor import TextureTransferProcessor, create_texture_transfer_processor, validate_texture_transfer_parameters

__all__ = [
    # 灰度转换
    "GrayscaleProcessor",
    "create_grayscale_processor",
    "validate_grayscale_parameters",

    # 锐化处理
    "SharpenProcessor",
    "create_sharpen_processor",
    "validate_sharpen_parameters",

    # 边缘检测
    "EdgeDetectionProcessor",
    "create_edge_detection_processor",
    "validate_edge_detection_parameters",

    # 伽马校正
    "GammaCorrectionProcessor",
    "create_gamma_correction_processor",
    "validate_gamma_correction_parameters",

    # 图像融合
    "ImageFusionProcessor",
    "create_image_fusion_processor",
    "validate_image_fusion_parameters",

    # 美颜增强
    "BeautyEnhancementProcessor",
    "create_beauty_enhancement_processor",
    "validate_beauty_enhancement_parameters",

    # 图像拼接
    "ImageStitchingProcessor",
    "create_image_stitching_processor",
    "validate_image_stitching_parameters",

    # 纹理迁移
    "TextureTransferProcessor",
    "create_texture_transfer_processor",
    "validate_texture_transfer_parameters"
]
