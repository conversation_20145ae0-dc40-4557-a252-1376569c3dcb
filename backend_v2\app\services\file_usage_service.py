"""
文件使用统计服务
负责跟踪文件的访问、下载等使用情况
"""
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List
from sqlalchemy.orm import Session
from sqlalchemy import func, and_

from app.models.file import File, FileStatus, FileType
from app.crud.crud_file import file as file_crud


class FileUsageService:
    """文件使用统计服务"""
    
    @staticmethod
    def record_file_access(db: Session, file_id: int, access_type: str = "view") -> bool:
        """
        记录文件访问
        
        Args:
            db: 数据库会话
            file_id: 文件ID
            access_type: 访问类型（view, download, preview等）
            
        Returns:
            是否记录成功
        """
        try:
            file_obj = file_crud.get(db=db, id=file_id)
            if not file_obj:
                return False

            # 获取现有元数据
            metadata = file_obj.file_metadata or {}
            
            # 初始化使用统计
            if "usage_stats" not in metadata:
                metadata["usage_stats"] = {
                    "total_accesses": 0,
                    "access_types": {},
                    "first_accessed": None,
                    "last_accessed": None,
                    "access_history": []
                }
            
            usage_stats = metadata["usage_stats"]
            current_time = datetime.now().isoformat()
            
            # 更新统计信息
            usage_stats["total_accesses"] += 1
            usage_stats["access_types"][access_type] = usage_stats["access_types"].get(access_type, 0) + 1
            usage_stats["last_accessed"] = current_time
            
            if usage_stats["first_accessed"] is None:
                usage_stats["first_accessed"] = current_time
            
            # 记录访问历史（保留最近50次）
            access_record = {
                "timestamp": current_time,
                "type": access_type
            }
            usage_stats["access_history"].append(access_record)
            
            # 只保留最近50次访问记录
            if len(usage_stats["access_history"]) > 50:
                usage_stats["access_history"] = usage_stats["access_history"][-50:]
            
            # 更新元数据
            metadata["usage_stats"] = usage_stats

            # 直接更新文件对象的元数据字段
            file_obj.file_metadata = metadata

            # 标记字段为已修改（对于JSONB字段很重要）
            from sqlalchemy.orm.attributes import flag_modified
            flag_modified(file_obj, 'file_metadata')

            db.add(file_obj)
            db.commit()
            db.refresh(file_obj)

            return True

        except Exception:
            return False
    
    @staticmethod
    def get_file_usage_stats(db: Session, file_id: int) -> Optional[Dict[str, Any]]:
        """
        获取文件使用统计
        
        Args:
            db: 数据库会话
            file_id: 文件ID
            
        Returns:
            使用统计信息
        """
        try:
            file_obj = file_crud.get(db=db, id=file_id)
            if not file_obj or not file_obj.file_metadata:
                return None

            usage_stats = file_obj.file_metadata.get("usage_stats")
            if not usage_stats:
                return {
                    "total_accesses": 0,
                    "access_types": {},
                    "first_accessed": None,
                    "last_accessed": None,
                    "never_accessed": True
                }
            
            # 计算访问频率
            if usage_stats.get("first_accessed") and usage_stats.get("last_accessed"):
                first_access = datetime.fromisoformat(usage_stats["first_accessed"])
                last_access = datetime.fromisoformat(usage_stats["last_accessed"])
                days_span = (last_access - first_access).days + 1
                
                usage_stats["access_frequency"] = {
                    "accesses_per_day": usage_stats["total_accesses"] / max(days_span, 1),
                    "days_since_first_access": days_span,
                    "days_since_last_access": (datetime.now() - last_access).days
                }
            
            return usage_stats
            
        except Exception:
            return None
    
    @staticmethod
    def get_popular_files(db: Session, limit: int = 10, days: int = 30) -> List[Dict[str, Any]]:
        """
        获取热门文件列表
        
        Args:
            db: 数据库会话
            limit: 返回数量限制
            days: 统计天数
            
        Returns:
            热门文件列表
        """
        try:
            # 查询所有有使用统计的文件
            files = db.query(File).filter(
                and_(
                    File.status != FileStatus.DELETED,
                    File.file_metadata.isnot(None)
                )
            ).all()
            
            popular_files = []
            cutoff_date = datetime.now() - timedelta(days=days)
            
            for file_obj in files:
                usage_stats = file_obj.file_metadata.get("usage_stats")
                if not usage_stats:
                    continue
                
                # 计算指定天数内的访问次数
                recent_accesses = 0
                access_history = usage_stats.get("access_history", [])
                
                for access in access_history:
                    try:
                        access_time = datetime.fromisoformat(access["timestamp"])
                        if access_time >= cutoff_date:
                            recent_accesses += 1
                    except (ValueError, KeyError):
                        continue
                
                if recent_accesses > 0:
                    popular_files.append({
                        "id": file_obj.id,
                        "filename": file_obj.filename,
                        "file_type": file_obj.file_type.value,
                        "file_size": file_obj.file_size,
                        "total_accesses": usage_stats.get("total_accesses", 0),
                        "recent_accesses": recent_accesses,
                        "last_accessed": usage_stats.get("last_accessed"),
                        "access_types": usage_stats.get("access_types", {})
                    })
            
            # 按最近访问次数排序
            popular_files.sort(key=lambda x: x["recent_accesses"], reverse=True)
            
            return popular_files[:limit]
            
        except Exception:
            return []
    
    @staticmethod
    def get_unused_files(db: Session, days: int = 90, limit: int = 100) -> List[Dict[str, Any]]:
        """
        获取未使用的文件列表
        
        Args:
            db: 数据库会话
            days: 未使用天数阈值
            limit: 返回数量限制
            
        Returns:
            未使用文件列表
        """
        try:
            cutoff_date = datetime.now() - timedelta(days=days)
            unused_files = []
            
            # 查询所有文件
            files = db.query(File).filter(
                File.status != FileStatus.DELETED
            ).limit(limit * 2).all()  # 多查询一些用于过滤
            
            for file_obj in files:
                usage_stats = file_obj.file_metadata.get("usage_stats") if file_obj.file_metadata else None
                
                is_unused = False
                
                if not usage_stats:
                    # 没有使用统计，检查创建时间
                    if file_obj.created_at < cutoff_date:
                        is_unused = True
                else:
                    # 有使用统计，检查最后访问时间
                    last_accessed = usage_stats.get("last_accessed")
                    if last_accessed:
                        try:
                            last_access_time = datetime.fromisoformat(last_accessed)
                            if last_access_time < cutoff_date:
                                is_unused = True
                        except ValueError:
                            # 解析失败，按创建时间判断
                            if file_obj.created_at < cutoff_date:
                                is_unused = True
                    else:
                        # 没有访问记录，按创建时间判断
                        if file_obj.created_at < cutoff_date:
                            is_unused = True
                
                if is_unused:
                    unused_files.append({
                        "id": file_obj.id,
                        "filename": file_obj.filename,
                        "file_type": file_obj.file_type.value,
                        "file_size": file_obj.file_size,
                        "created_at": file_obj.created_at,
                        "last_accessed": usage_stats.get("last_accessed") if usage_stats else None,
                        "total_accesses": usage_stats.get("total_accesses", 0) if usage_stats else 0,
                        "days_unused": (datetime.now() - (
                            datetime.fromisoformat(usage_stats["last_accessed"]) 
                            if usage_stats and usage_stats.get("last_accessed") 
                            else file_obj.created_at
                        )).days
                    })
                
                if len(unused_files) >= limit:
                    break
            
            # 按未使用天数排序
            unused_files.sort(key=lambda x: x["days_unused"], reverse=True)
            
            return unused_files
            
        except Exception:
            return []
    
    @staticmethod
    def get_usage_summary(db: Session) -> Dict[str, Any]:
        """
        获取使用情况摘要
        
        Args:
            db: 数据库会话
            
        Returns:
            使用情况摘要
        """
        try:
            # 查询所有文件
            total_files = db.query(File).filter(File.status != FileStatus.DELETED).count()
            
            files_with_stats = db.query(File).filter(
                and_(
                    File.status != FileStatus.DELETED,
                    File.file_metadata.isnot(None)
                )
            ).all()
            
            accessed_files = 0
            total_accesses = 0
            access_types_summary = {}
            
            for file_obj in files_with_stats:
                usage_stats = file_obj.file_metadata.get("usage_stats")
                if usage_stats and usage_stats.get("total_accesses", 0) > 0:
                    accessed_files += 1
                    total_accesses += usage_stats.get("total_accesses", 0)
                    
                    # 统计访问类型
                    for access_type, count in usage_stats.get("access_types", {}).items():
                        access_types_summary[access_type] = access_types_summary.get(access_type, 0) + count
            
            never_accessed = total_files - accessed_files
            
            return {
                "total_files": total_files,
                "accessed_files": accessed_files,
                "never_accessed": never_accessed,
                "access_rate": (accessed_files / total_files * 100) if total_files > 0 else 0,
                "total_accesses": total_accesses,
                "average_accesses_per_file": (total_accesses / accessed_files) if accessed_files > 0 else 0,
                "access_types_summary": access_types_summary,
                "generated_at": datetime.now().isoformat()
            }
            
        except Exception:
            return {
                "total_files": 0,
                "accessed_files": 0,
                "never_accessed": 0,
                "access_rate": 0,
                "total_accesses": 0,
                "average_accesses_per_file": 0,
                "access_types_summary": {},
                "generated_at": datetime.now().isoformat()
            }
