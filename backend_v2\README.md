# Backend V2 - FastAPI重构版本

这是基于FastAPI的新后端实现，采用现代化的微服务架构。

## 项目结构

```
backend_v2/
├── app/                    # 应用主目录
│   ├── __init__.py
│   ├── main.py            # FastAPI应用入口
│   ├── api/               # API路由
│   │   ├── __init__.py
│   │   ├── deps.py        # 依赖注入
│   │   ├── tasks.py       # 任务管理API
│   │   └── files.py       # 文件管理API
│   ├── core/              # 核心配置
│   │   ├── __init__.py
│   │   ├── config.py      # 配置管理
│   │   └── database.py    # 数据库配置
│   ├── models/            # 数据模型
│   │   ├── __init__.py
│   │   ├── base.py        # 基础模型
│   │   ├── task.py        # 任务模型
│   │   ├── file.py        # 文件模型
│   │   ├── batch.py       # 批次模型
│   │   ├── result.py      # 结果模型
│   │   └── user_config.py # 系统配置模型
│   ├── schemas/           # Pydantic模式
│   │   ├── __init__.py
│   │   ├── task.py        # 任务模式
│   │   ├── file.py        # 文件模式
│   │   ├── batch.py       # 批次模式
│   │   ├── result.py      # 结果模式
│   │   ├── task_submit.py # 任务提交模式
│   │   ├── task_parameters.py # 任务参数模式
│   │   └── user_config.py # 系统配置模式
│   ├── services/          # 业务逻辑
│   │   ├── __init__.py
│   │   ├── task_service.py    # 任务服务
│   │   ├── file_service.py    # 文件服务
│   │   └── storage_service.py # 存储服务
│   ├── workers/           # Celery任务
│   │   ├── __init__.py
│   │   ├── celery_app.py  # Celery配置
│   │   ├── image_tasks.py # 图像处理任务
│   │   └── video_tasks.py # 视频处理任务
│   └── utils/             # 工具函数
│       ├── __init__.py
│       ├── logger.py      # 日志工具
│       └── helpers.py     # 辅助函数
├── tests/                 # 测试目录
│   ├── __init__.py
│   ├── conftest.py        # 测试配置
│   ├── test_api/          # API测试
│   ├── test_models/       # 模型测试
│   └── test_services/     # 服务测试
├── alembic/               # 数据库迁移
│   ├── versions/
│   ├── env.py
│   └── script.py.mako
├── docker/                # Docker配置
│   ├── Dockerfile
│   ├── docker-compose.yml
│   └── docker-compose.dev.yml
├── scripts/               # 脚本目录
│   ├── start.sh           # 启动脚本
│   └── migrate.sh         # 迁移脚本
├── requirements.txt       # 依赖文件
├── pyproject.toml         # 项目配置
├── alembic.ini           # Alembic配置
└── .env.example          # 环境变量示例
```

## 技术栈

- **Web框架**: FastAPI 0.104+
- **数据库**: PostgreSQL 15 + SQLAlchemy 2.0
- **任务队列**: Celery 5.4 + Redis
- **对象存储**: MinIO (S3兼容)
- **文档**: OpenAPI/Swagger自动生成
- **测试**: pytest + httpx
- **容器化**: Docker + docker-compose

## 开发环境设置

### 快速开始（推荐）

使用初始化脚本一键设置：

**Linux/macOS:**
```bash
cd backend_v2
chmod +x scripts/setup.sh
./scripts/setup.sh
```

**Windows (PowerShell):**
```powershell
cd backend_v2
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
.\scripts\setup.ps1
```

### 手动设置

1. 安装uv（如果尚未安装）：
   ```bash
   curl -LsSf https://astral.sh/uv/install.sh | sh
   ```

2. 初始化项目并安装依赖：
   ```bash
   cd backend_v2
   uv lock  # 生成uv.lock文件
   uv sync --no-editable  # 安装项目依赖（不作为可编辑包）
   ```

3. 复制环境变量文件：
   ```bash
   cp .env.example .env
   ```

4. 启动开发环境（数据库等服务）：
   ```bash
   docker-compose -f docker/docker-compose.dev.yml up -d
   ```

5. 运行数据库迁移：
   ```bash
   uv run alembic upgrade head
   ```

6. 启动应用：
   ```bash
   uv run uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
   ```

### 或者使用Docker完整环境

1. 复制环境变量文件：
   ```bash
   cp .env.example .env
   ```

2. 启动完整开发环境：
   ```bash
   docker-compose -f docker/docker-compose.dev.yml up -d
   ```

## API文档

启动应用后，访问以下地址查看API文档：
- Swagger UI: http://localhost:8000/docs
- ReDoc: http://localhost:8000/redoc

## 测试

运行测试：
```bash
uv run pytest tests/ -v
```

运行测试覆盖率：
```bash
uv run pytest tests/ --cov=app --cov-report=html
```

## 开发工具

代码格式化：
```bash
uv run black app/ tests/
uv run isort app/ tests/
```

代码检查：
```bash
uv run flake8 app/ tests/
uv run mypy app/
```
