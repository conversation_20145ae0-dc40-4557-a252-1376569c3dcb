"""
图像融合处理器
实现双图像融合功能，支持权重调节的图像混合
"""
from typing import Dict, Any, List, Tuple
import cv2
import numpy as np
import structlog
from sqlalchemy.orm import Session

from app.services.image_processor_base import ImageProcessorBase, ParameterConverter
from app.services.minio_service import MinIOService
from app.models.file import File

logger = structlog.get_logger()


class ImageFusionProcessor(ImageProcessorBase):
    """图像融合处理器 - 处理双文件输入"""
    
    def process_multiple_images(
        self, 
        file_objects: List[File], 
        parameters: Dict[str, Any],
        operation_name: str,
        task_id: str = None
    ) -> Dict[str, Any]:
        """
        处理多个图像的融合
        
        Args:
            file_objects: 文件对象列表（应该包含2个文件）
            parameters: 处理参数
            operation_name: 操作名称
            task_id: 任务ID
            
        Returns:
            处理结果字典
        """
        import time
        start_time = time.time()
        
        try:
            if len(file_objects) != 2:
                raise ValueError(f"Image fusion requires exactly 2 images, got {len(file_objects)}")
            
            # 更新进度：开始下载
            if task_id:
                self.update_task_progress(task_id, 10, 100, "Downloading images...")
            
            # 下载两个图像
            image1, temp_path1 = self.download_image_from_minio(file_objects[0])
            image2, temp_path2 = self.download_image_from_minio(file_objects[1])
            
            # 更新进度：开始处理
            if task_id:
                self.update_task_progress(task_id, 30, 100, "Processing image fusion...")
            
            # 处理图像融合
            fused_image = self.process_image_fusion(image1, image2, parameters)
            
            # 更新进度：开始上传
            if task_id:
                self.update_task_progress(task_id, 70, 100, "Uploading result...")
            
            # 计算处理时间
            processing_time = time.time() - start_time
            
            # 上传结果（使用第一个文件作为基础信息）
            result = self.upload_processed_image(
                processed_image=fused_image,
                original_file=file_objects[0],
                operation_name=operation_name,
                parameters=parameters,
                processing_time=processing_time
            )
            
            # 在元数据中添加第二个文件的信息
            result["metadata"]["second_file_id"] = file_objects[1].id
            result["metadata"]["fusion_type"] = "weighted_blend"
            
            # 更新进度：完成
            if task_id:
                self.update_task_progress(task_id, 100, 100, "Fusion completed")
            
            logger.info("image_fusion_completed",
                       file1_id=file_objects[0].id,
                       file2_id=file_objects[1].id,
                       processing_time=processing_time)
            
            return result
            
        except Exception as e:
            logger.error("image_fusion_failed",
                        file_objects=[f.id for f in file_objects],
                        error=str(e))
            raise
        finally:
            # 确保清理临时文件
            self.cleanup_temp_files()
    
    def process_image_fusion(
        self, 
        image1: np.ndarray, 
        image2: np.ndarray, 
        parameters: Dict[str, Any]
    ) -> np.ndarray:
        """
        执行图像融合算法
        
        Args:
            image1: 第一张图像
            image2: 第二张图像
            parameters: 融合参数
                - weight1: 第一张图像权重（0.0-1.0）
                - weight2: 第二张图像权重（0.0-1.0）
                
        Returns:
            融合后的图像
        """
        try:
            weight1 = parameters.get('weight1', 0.5)
            weight2 = parameters.get('weight2', 0.5)
            
            logger.info("processing_image_fusion", 
                       image1_shape=image1.shape,
                       image2_shape=image2.shape,
                       weight1=weight1,
                       weight2=weight2)
            
            # 调整图像大小使其一致（使用较小的尺寸）
            height = min(image1.shape[0], image2.shape[0])
            width = min(image1.shape[1], image2.shape[1])
            
            image1_resized = cv2.resize(image1, (width, height))
            image2_resized = cv2.resize(image2, (width, height))
            
            # 执行加权融合
            fused = cv2.addWeighted(image1_resized, weight1, image2_resized, weight2, 0)
            
            logger.info("image_fusion_algorithm_completed",
                       output_shape=fused.shape,
                       final_weights=(weight1, weight2))
            
            return fused
            
        except Exception as e:
            logger.error("image_fusion_algorithm_failed", 
                        weight1=weight1,
                        weight2=weight2,
                        error=str(e))
            raise ValueError(f"Image fusion algorithm failed: {str(e)}")
    
    def process_image(self, image: np.ndarray, parameters: Dict[str, Any]) -> np.ndarray:
        """
        单图像处理接口（图像融合不适用）
        """
        raise NotImplementedError("Image fusion requires two images. Use process_multiple_images instead.")


def create_image_fusion_processor(minio_service: MinIOService, db_session: Session) -> ImageFusionProcessor:
    """
    创建图像融合处理器实例
    
    Args:
        minio_service: MinIO服务实例
        db_session: 数据库会话
        
    Returns:
        图像融合处理器实例
    """
    return ImageFusionProcessor(minio_service, db_session)


def validate_image_fusion_parameters(parameters: Dict[str, Any]) -> Dict[str, Any]:
    """
    验证和转换图像融合处理参数
    
    Args:
        parameters: 输入参数
        
    Returns:
        验证后的参数
        
    Raises:
        ValueError: 参数无效时抛出
    """
    try:
        # 提取权重参数
        weight1 = parameters.get('weight1', 0.5)
        weight2 = parameters.get('weight2', 0.5)
        
        # 类型转换
        try:
            weight1 = float(weight1)
            weight2 = float(weight2)
        except (ValueError, TypeError):
            raise ValueError("Weights must be numbers")
        
        # 范围检查
        if not (0.0 <= weight1 <= 1.0):
            raise ValueError("Weight1 must be between 0.0 and 1.0")
        
        if not (0.0 <= weight2 <= 1.0):
            raise ValueError("Weight2 must be between 0.0 and 1.0")
        
        # 权重和检查
        weight_sum = weight1 + weight2
        if abs(weight_sum - 1.0) > 0.001:
            # 自动归一化权重
            if weight_sum > 0:
                weight1 = weight1 / weight_sum
                weight2 = weight2 / weight_sum
                logger.info("weights_normalized", 
                           original_sum=weight_sum,
                           normalized_weights=(weight1, weight2))
            else:
                # 如果权重和为0，使用默认值
                weight1, weight2 = 0.5, 0.5
                logger.warning("zero_weight_sum_detected", 
                              fallback_weights=(weight1, weight2))
        
        return {
            'weight1': weight1,
            'weight2': weight2
        }
        
    except Exception as e:
        logger.error("image_fusion_parameter_validation_failed", 
                    parameters=parameters,
                    error=str(e))
        raise ValueError(f"Parameter validation failed: {str(e)}")


# 为了向后兼容，提供旧格式的函数接口
def image_fusion_legacy(file1_path: str, file2_path: str, weight1: float = 0.5, weight2: float = 0.5) -> str:
    """
    旧格式的图像融合函数接口（向后兼容）
    
    Args:
        file1_path: 第一张图像文件路径
        file2_path: 第二张图像文件路径
        weight1: 第一张图像权重
        weight2: 第二张图像权重
        
    Returns:
        处理后的图像路径
        
    Note:
        这个函数仅用于向后兼容，新代码应使用ImageFusionProcessor
    """
    logger.warning("using_legacy_image_fusion_interface", 
                  file1_path=file1_path,
                  file2_path=file2_path,
                  weight1=weight1,
                  weight2=weight2)
    
    # 这里可以实现向旧接口的适配逻辑
    # 但在新架构中，建议直接使用ImageFusionProcessor
    raise NotImplementedError("Legacy interface not implemented in new architecture. Use ImageFusionProcessor instead.")
