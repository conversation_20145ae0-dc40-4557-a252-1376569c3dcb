"""
任务管理服务
集成状态转换逻辑的高级任务管理功能
"""

from typing import Optional, Dict, Any, List
from sqlalchemy.orm import Session
import structlog

from app.models.task import Task, TaskStatus
from app.models.batch import Batch, BatchStatus
from app.crud.crud_task import task as crud_task
from app.crud.crud_batch import batch as crud_batch
from app.services.task_state_transition import (
    TaskStateTransitionService, 
    BatchStateTransitionService,
    StateTransitionError
)

logger = structlog.get_logger()


class TaskManagementService:
    """任务管理服务"""
    
    @staticmethod
    def transition_task_status(
        db: Session,
        task_id: int,
        target_status: TaskStatus,
        reason: Optional[str] = None,
        user_id: Optional[int] = None,
        error_message: Optional[str] = None
    ) -> Task:
        """
        安全地转换任务状态
        
        Args:
            db: 数据库会话
            task_id: 任务ID
            target_status: 目标状态
            reason: 转换原因
            user_id: 用户ID
            error_message: 错误信息（如果转换到失败状态）
            
        Returns:
            更新后的任务对象
            
        Raises:
            StateTransitionError: 状态转换不允许时
        """
        # 获取任务
        task = crud_task.get(db=db, id=task_id)
        if not task:
            raise StateTransitionError(f"任务 {task_id} 不存在")
        
        current_status = task.status
        
        # 验证状态转换
        is_valid, error_msg = TaskStateTransitionService.validate_transition(
            current_status, target_status
        )
        
        if not is_valid:
            raise StateTransitionError(error_msg)
        
        # 创建转换日志
        transition_log = TaskStateTransitionService.create_transition_log(
            task_id=task_id,
            current_status=current_status,
            target_status=target_status,
            reason=reason,
            user_id=user_id
        )
        
        # 记录状态转换
        logger.info(
            "task_status_transition",
            task_id=task_id,
            from_status=current_status.value,
            to_status=target_status.value,
            reason=reason,
            user_id=user_id
        )
        
        # 更新任务状态
        update_data = {"status": target_status}
        if error_message and target_status == TaskStatus.FAILED:
            update_data["error_message"] = error_message
        
        updated_task = crud_task.update(db=db, db_obj=task, obj_in=update_data)
        
        # 如果任务配置中没有状态转换历史，则创建
        if not updated_task.config:
            updated_task.config = {}
        
        if "status_history" not in updated_task.config:
            updated_task.config["status_history"] = []
        
        # 添加转换记录到任务配置
        updated_task.config["status_history"].append(transition_log)
        
        # 保存配置更新
        crud_task.update(db=db, db_obj=updated_task, obj_in={"config": updated_task.config})
        
        return updated_task
    
    @staticmethod
    def pause_task(
        db: Session,
        task_id: int,
        user_id: Optional[int] = None
    ) -> Task:
        """
        暂停任务
        
        Args:
            db: 数据库会话
            task_id: 任务ID
            user_id: 用户ID
            
        Returns:
            更新后的任务对象
        """
        return TaskManagementService.transition_task_status(
            db=db,
            task_id=task_id,
            target_status=TaskStatus.PAUSED,
            reason="用户暂停任务",
            user_id=user_id
        )
    
    @staticmethod
    def resume_task(
        db: Session,
        task_id: int,
        user_id: Optional[int] = None
    ) -> Task:
        """
        恢复任务
        
        Args:
            db: 数据库会话
            task_id: 任务ID
            user_id: 用户ID
            
        Returns:
            更新后的任务对象
        """
        return TaskManagementService.transition_task_status(
            db=db,
            task_id=task_id,
            target_status=TaskStatus.IN_PROGRESS,
            reason="用户恢复任务",
            user_id=user_id
        )
    
    @staticmethod
    def cancel_task(
        db: Session,
        task_id: int,
        user_id: Optional[int] = None
    ) -> Task:
        """
        取消任务
        
        Args:
            db: 数据库会话
            task_id: 任务ID
            user_id: 用户ID
            
        Returns:
            更新后的任务对象
        """
        # 首先转换到CANCELLING状态
        task = TaskManagementService.transition_task_status(
            db=db,
            task_id=task_id,
            target_status=TaskStatus.CANCELLING,
            reason="用户请求取消任务",
            user_id=user_id
        )
        
        # 通知Celery取消相关的任务
        from app.services.task_dispatcher import TaskDispatcher
        celery_cancelled = TaskDispatcher.cancel_task_celery_jobs(db=db, task_id=task_id)
        if not celery_cancelled:
            logger.warning("celery_cancel_failed_in_task_management", task_id=task_id)

        # 转换到CANCELLED状态
        return TaskManagementService.transition_task_status(
            db=db,
            task_id=task_id,
            target_status=TaskStatus.CANCELLED,
            reason="任务取消完成",
            user_id=user_id
        )
    
    @staticmethod
    def start_task(
        db: Session,
        task_id: int
    ) -> Task:
        """
        开始任务处理
        
        Args:
            db: 数据库会话
            task_id: 任务ID
            
        Returns:
            更新后的任务对象
        """
        return TaskManagementService.transition_task_status(
            db=db,
            task_id=task_id,
            target_status=TaskStatus.IN_PROGRESS,
            reason="开始处理任务"
        )
    
    @staticmethod
    def complete_task(
        db: Session,
        task_id: int,
        success: bool = True,
        error_message: Optional[str] = None
    ) -> Task:
        """
        完成任务
        
        Args:
            db: 数据库会话
            task_id: 任务ID
            success: 是否成功
            error_message: 错误信息
            
        Returns:
            更新后的任务对象
        """
        target_status = TaskStatus.SUCCESS if success else TaskStatus.FAILED
        reason = "任务处理成功" if success else "任务处理失败"
        
        return TaskManagementService.transition_task_status(
            db=db,
            task_id=task_id,
            target_status=target_status,
            reason=reason,
            error_message=error_message
        )
    
    @staticmethod
    def get_task_status_history(
        db: Session,
        task_id: int
    ) -> List[Dict[str, Any]]:
        """
        获取任务状态转换历史
        
        Args:
            db: 数据库会话
            task_id: 任务ID
            
        Returns:
            状态转换历史列表
        """
        task = crud_task.get(db=db, id=task_id)
        if not task or not task.config:
            return []
        
        return task.config.get("status_history", [])
    
    @staticmethod
    def get_task_allowed_actions(
        db: Session,
        task_id: int
    ) -> List[str]:
        """
        获取任务当前允许的操作
        
        Args:
            db: 数据库会话
            task_id: 任务ID
            
        Returns:
            允许的操作列表
        """
        task = crud_task.get(db=db, id=task_id)
        if not task:
            return []
        
        current_status = task.status
        actions = []
        
        if TaskStateTransitionService.can_be_paused(current_status):
            actions.append("pause")
        
        if TaskStateTransitionService.can_be_resumed(current_status):
            actions.append("resume")
        
        if TaskStateTransitionService.can_be_cancelled(current_status):
            actions.append("cancel")
        
        return actions


class BatchManagementService:
    """批次管理服务"""

    @staticmethod
    def transition_batch_status(
        db: Session,
        batch_id: int,
        target_status: BatchStatus,
        reason: Optional[str] = None
    ) -> Batch:
        """
        安全地转换批次状态

        Args:
            db: 数据库会话
            batch_id: 批次ID
            target_status: 目标状态
            reason: 转换原因

        Returns:
            更新后的批次对象
        """
        # 获取批次
        batch = crud_batch.get(db=db, id=batch_id)
        if not batch:
            raise StateTransitionError(f"批次 {batch_id} 不存在")

        current_status = batch.status

        # 验证状态转换
        is_valid, error_msg = BatchStateTransitionService.validate_transition(
            current_status, target_status
        )

        if not is_valid:
            raise StateTransitionError(error_msg)

        # 记录状态转换
        logger.info(
            "batch_status_transition",
            batch_id=batch_id,
            from_status=current_status.value,
            to_status=target_status.value,
            reason=reason
        )

        # 更新批次状态
        updated_batch = crud_batch.update(
            db=db,
            db_obj=batch,
            obj_in={"status": target_status}
        )

        return updated_batch
