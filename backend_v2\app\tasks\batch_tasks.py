"""
批次处理任务
处理批次中的文件并更新状态
"""

from typing import Dict, List, Any
from celery import current_task
import structlog
from datetime import datetime

from app.core.celery_app import celery_app
from app.core.database import get_db
from app.models.batch import BatchStatus
from app.models.file import FileStatus
from app.services.task_management import BatchManagementService
from app.crud.crud_batch import batch as crud_batch
from app.crud.crud_file import file as crud_file

logger = structlog.get_logger()


@celery_app.task(bind=True, name="app.tasks.batch_tasks.process_batch")
def process_batch(self, batch_params: Dict[str, Any]):
    """
    处理批次任务
    
    Args:
        batch_params: 批次参数，包含task_id, batch_id, task_type, parameters, files
    """
    task_id = batch_params.get("task_id")
    batch_id = batch_params.get("batch_id")
    task_type = batch_params.get("task_type")
    parameters = batch_params.get("parameters", {})
    files = batch_params.get("files", [])
    
    logger.info(
        "batch_processing_started",
        celery_task_id=self.request.id,
        task_id=task_id,
        batch_id=batch_id,
        task_type=task_type,
        file_count=len(files)
    )
    
    try:
        # 获取数据库会话
        db = next(get_db())
        
        # 更新批次状态为处理中
        BatchManagementService.transition_batch_status(
            db=db,
            batch_id=batch_id,
            target_status=BatchStatus.IN_PROGRESS,
            reason="开始批次处理"
        )
        
        # 处理批次中的每个文件
        processed_files = 0
        failed_files = 0
        
        for i, file_info in enumerate(files):
            file_id = file_info["file_id"]
            filename = file_info["filename"]
            storage_path = file_info["storage_path"]
            
            try:
                # 更新任务进度
                progress = (i / len(files)) * 100
                self.update_state(
                    state="PROGRESS",
                    meta={
                        "current": i,
                        "total": len(files),
                        "progress": progress,
                        "status": f"处理文件: {filename}"
                    }
                )
                
                logger.info(
                    "processing_file",
                    celery_task_id=self.request.id,
                    batch_id=batch_id,
                    file_id=file_id,
                    filename=filename,
                    progress=progress
                )
                
                # 根据任务类型处理文件
                result = process_file_by_type(
                    task_type=task_type,
                    file_info=file_info,
                    parameters=parameters
                )
                
                if result["success"]:
                    # 更新文件状态为已处理
                    file_obj = crud_file.get(db=db, id=file_id)
                    if file_obj:
                        crud_file.update(
                            db=db,
                            db_obj=file_obj,
                            obj_in={
                                "status": FileStatus.PROCESSED,
                                "result_path": result.get("output_path"),
                                "processing_metadata": {
                                    "processed_at": datetime.now().isoformat(),
                                    "processing_time": result.get("processing_time"),
                                    "celery_task_id": self.request.id
                                }
                            }
                        )
                    processed_files += 1
                else:
                    # 更新文件状态为失败
                    file_obj = crud_file.get(db=db, id=file_id)
                    if file_obj:
                        crud_file.update(
                            db=db,
                            db_obj=file_obj,
                            obj_in={
                                "status": FileStatus.FAILED,
                                "error_message": result.get("error"),
                                "processing_metadata": {
                                    "failed_at": datetime.now().isoformat(),
                                    "celery_task_id": self.request.id
                                }
                            }
                        )
                    failed_files += 1
                    
                    logger.error(
                        "file_processing_failed",
                        celery_task_id=self.request.id,
                        batch_id=batch_id,
                        file_id=file_id,
                        filename=filename,
                        error=result.get("error")
                    )
                
            except Exception as e:
                failed_files += 1
                logger.error(
                    "file_processing_exception",
                    celery_task_id=self.request.id,
                    batch_id=batch_id,
                    file_id=file_id,
                    filename=filename,
                    error=str(e)
                )
                
                # 更新文件状态为失败
                try:
                    file_obj = crud_file.get(db=db, id=file_id)
                    if file_obj:
                        crud_file.update(
                            db=db,
                            db_obj=file_obj,
                            obj_in={
                                "status": FileStatus.FAILED,
                                "error_message": str(e),
                                "processing_metadata": {
                                    "failed_at": datetime.now().isoformat(),
                                    "celery_task_id": self.request.id
                                }
                            }
                        )
                except Exception as update_error:
                    logger.error("failed_to_update_file_status", file_id=file_id, error=str(update_error))
        
        # 更新批次统计信息
        batch_obj = crud_batch.get(db=db, id=batch_id)
        if batch_obj:
            crud_batch.update(
                db=db,
                db_obj=batch_obj,
                obj_in={
                    "processed_files": processed_files,
                    "progress": 100.0,
                    "processing_metadata": {
                        "completed_at": datetime.now().isoformat(),
                        "processed_files": processed_files,
                        "failed_files": failed_files,
                        "celery_task_id": self.request.id
                    }
                }
            )
        
        # 确定批次最终状态
        if failed_files == 0:
            final_status = BatchStatus.SUCCESS
            reason = "批次处理成功"
        elif processed_files == 0:
            final_status = BatchStatus.FAILED
            reason = "批次处理完全失败"
        else:
            final_status = BatchStatus.SUCCESS  # 部分成功也算成功
            reason = f"批次处理完成，成功{processed_files}个，失败{failed_files}个"
        
        # 更新批次最终状态
        BatchManagementService.transition_batch_status(
            db=db,
            batch_id=batch_id,
            target_status=final_status,
            reason=reason
        )
        
        # 提交数据库更改
        db.commit()
        
        # 返回处理结果
        result = {
            "batch_id": batch_id,
            "task_id": task_id,
            "processed_files": processed_files,
            "failed_files": failed_files,
            "total_files": len(files),
            "success_rate": (processed_files / len(files)) * 100 if files else 0,
            "status": final_status.value,
            "completed_at": datetime.now().isoformat()
        }
        
        logger.info(
            "batch_processing_completed",
            celery_task_id=self.request.id,
            **result
        )
        
        return result
        
    except Exception as e:
        logger.error(
            "batch_processing_failed",
            celery_task_id=self.request.id,
            task_id=task_id,
            batch_id=batch_id,
            error=str(e)
        )
        
        # 更新批次状态为失败
        try:
            db = next(get_db())
            BatchManagementService.transition_batch_status(
                db=db,
                batch_id=batch_id,
                target_status=BatchStatus.FAILED,
                reason=f"批次处理异常: {str(e)}"
            )
            db.commit()
        except Exception as update_error:
            logger.error("failed_to_update_batch_status", batch_id=batch_id, error=str(update_error))
        
        # 重新抛出异常，让Celery处理重试
        raise


def process_file_by_type(
    task_type: str,
    file_info: Dict[str, Any],
    parameters: Dict[str, Any]
) -> Dict[str, Any]:
    """
    根据任务类型处理文件
    
    Args:
        task_type: 任务类型
        file_info: 文件信息
        parameters: 处理参数
        
    Returns:
        处理结果
    """
    try:
        # TODO: 这里应该调用具体的处理函数
        # 目前返回模拟结果
        
        logger.info(
            "simulating_file_processing",
            task_type=task_type,
            filename=file_info["filename"],
            parameters=parameters
        )
        
        # 模拟处理时间
        import time
        time.sleep(1)  # 模拟1秒处理时间
        
        # 模拟成功结果
        return {
            "success": True,
            "output_path": f"processed/{file_info['filename']}",
            "processing_time": 1.0,
            "metadata": {
                "task_type": task_type,
                "parameters": parameters,
                "original_file": file_info["filename"]
            }
        }
        
    except Exception as e:
        return {
            "success": False,
            "error": str(e),
            "processing_time": 0.0
        }


@celery_app.task(name="app.tasks.batch_tasks.monitor_batch_progress")
def monitor_batch_progress(task_id: int):
    """
    监控批次进度并更新任务状态
    
    Args:
        task_id: 任务ID
    """
    try:
        db = next(get_db())
        
        # 更新任务进度
        from app.services.task_persistence import TaskPersistenceService
        updated_task = TaskPersistenceService.update_task_progress(db=db, task_id=task_id)
        
        if updated_task:
            logger.info(
                "task_progress_updated",
                task_id=task_id,
                progress=updated_task.progress,
                status=updated_task.status.value
            )
        
        db.commit()
        
    except Exception as e:
        logger.error("monitor_batch_progress_failed", task_id=task_id, error=str(e))
