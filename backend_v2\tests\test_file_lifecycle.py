#!/usr/bin/env python3
"""
测试文件生命周期管理功能
"""
import sys
import os
import io
from datetime import datetime, timedelta
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from fastapi.testclient import TestClient
from app.main import app
from app.core.database import get_db_session
from app.crud.crud_file import file as file_crud
from app.services.file_lifecycle_service import FileLifecycleService, FileLifecyclePolicy
from app.models.file import FileStatus

def test_lifecycle_policies():
    """测试文件生命周期策略"""
    print("🧪 测试文件生命周期策略...")
    
    try:
        # 测试路径策略识别
        test_cases = [
            ("uploads/test.jpg", FileLifecyclePolicy.RAW_FILES),
            ("processed/result.jpg", FileLifecyclePolicy.RESULT_FILES),
            ("thumbnails/thumb.jpg", FileLifecyclePolicy.THUMBNAIL_FILES),
            ("temp/temp.jpg", FileLifecyclePolicy.TEMP_FILES),
            ("unknown/path.jpg", FileLifecyclePolicy.RAW_FILES)  # 默认策略
        ]
        
        for path, expected_policy in test_cases:
            actual_policy = FileLifecycleService.get_file_policy(path)
            if actual_policy == expected_policy:
                print(f"✅ 路径 '{path}' -> 策略 '{actual_policy.value}'")
            else:
                print(f"❌ 路径 '{path}' 期望 '{expected_policy.value}' 但得到 '{actual_policy.value}'")
                return False
        
        # 测试过期时间计算
        now = datetime.now()
        
        # 原始文件：90天
        raw_expiry = FileLifecycleService.calculate_expiry_date(now, FileLifecyclePolicy.RAW_FILES)
        expected_raw = now + timedelta(days=90)
        if abs((raw_expiry - expected_raw).total_seconds()) < 60:  # 允许1分钟误差
            print(f"✅ 原始文件过期时间计算正确: {raw_expiry}")
        else:
            print(f"❌ 原始文件过期时间计算错误")
            return False
        
        # 结果文件：永久保存
        result_expiry = FileLifecycleService.calculate_expiry_date(now, FileLifecyclePolicy.RESULT_FILES)
        if result_expiry is None:
            print(f"✅ 结果文件永久保存策略正确")
        else:
            print(f"❌ 结果文件应该永久保存")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_lifecycle_statistics():
    """测试生命周期统计API"""
    print("\n🧪 测试生命周期统计API...")
    
    try:
        client = TestClient(app)
        
        print("🔄 获取生命周期统计...")
        stats_response = client.get("/api/files/lifecycle/statistics")
        
        print(f"📡 统计响应状态码: {stats_response.status_code}")
        
        if stats_response.status_code == 200:
            stats = stats_response.json()
            print(f"✅ 生命周期统计获取成功!")
            print(f"📊 总文件数: {stats.get('total_files', 0)}")
            
            by_policy = stats.get('by_policy', {})
            print(f"📂 按策略分布:")
            for policy, policy_stats in by_policy.items():
                print(f"   - {policy}: {policy_stats.get('count', 0)} 个文件")
                print(f"     过期: {policy_stats.get('expired', 0)} 个")
                print(f"     即将过期: {policy_stats.get('expiring_soon', 0)} 个")
                print(f"     保留天数: {policy_stats.get('retention_days', 'N/A')}")
            
            expiry_summary = stats.get('expiry_summary', {})
            print(f"📊 过期摘要:")
            print(f"   - 已过期: {expiry_summary.get('expired', 0)} 个")
            print(f"   - 即将过期: {expiry_summary.get('expiring_soon', 0)} 个")
            print(f"   - 永久保存: {expiry_summary.get('permanent', 0)} 个")
            
            return True
        else:
            print(f"❌ 统计获取失败: {stats_response.status_code}")
            print(f"错误详情: {stats_response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        return False

def test_expired_files_api():
    """测试过期文件查询API"""
    print("\n🧪 测试过期文件查询API...")
    
    try:
        client = TestClient(app)
        
        print("🔄 获取过期文件列表...")
        expired_response = client.get("/api/files/lifecycle/expired?limit=10")
        
        print(f"📡 过期文件响应状态码: {expired_response.status_code}")
        
        if expired_response.status_code == 200:
            result = expired_response.json()
            print(f"✅ 过期文件查询成功!")
            print(f"🔍 发现 {result.get('total_found', 0)} 个过期文件")
            
            expired_files = result.get('expired_files', [])
            for file_info in expired_files[:3]:  # 只显示前3个
                print(f"   - 文件ID: {file_info.get('id')}")
                print(f"     文件名: {file_info.get('filename')}")
                print(f"     策略: {file_info.get('policy')}")
                print(f"     过期天数: {file_info.get('days_expired')}")
            
            return True
        else:
            print(f"❌ 过期文件查询失败: {expired_response.status_code}")
            print(f"错误详情: {expired_response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        return False

def test_cleanup_task_trigger():
    """测试清理任务触发API"""
    print("\n🧪 测试清理任务触发API...")
    
    try:
        client = TestClient(app)
        
        print("🔄 触发文件清理任务（试运行）...")
        cleanup_response = client.post("/api/files/lifecycle/cleanup?dry_run=true")
        
        print(f"📡 清理任务响应状态码: {cleanup_response.status_code}")
        
        if cleanup_response.status_code == 200:
            result = cleanup_response.json()
            print(f"✅ 清理任务触发成功!")
            print(f"🆔 任务ID: {result.get('task_id')}")
            print(f"📊 状态: {result.get('status')}")
            print(f"🧪 试运行: {result.get('dry_run')}")
            print(f"💬 消息: {result.get('message')}")
            
            return True
        else:
            print(f"❌ 清理任务触发失败: {cleanup_response.status_code}")
            print(f"错误详情: {cleanup_response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        return False

def test_archival_task_trigger():
    """测试归档任务触发API"""
    print("\n🧪 测试归档任务触发API...")
    
    try:
        client = TestClient(app)
        
        print("🔄 触发文件归档任务...")
        archive_response = client.post("/api/files/lifecycle/archive?archive_days=365")
        
        print(f"📡 归档任务响应状态码: {archive_response.status_code}")
        
        if archive_response.status_code == 200:
            result = archive_response.json()
            print(f"✅ 归档任务触发成功!")
            print(f"🆔 任务ID: {result.get('task_id')}")
            print(f"📊 状态: {result.get('status')}")
            print(f"📅 归档天数: {result.get('archive_days')}")
            print(f"💬 消息: {result.get('message')}")
            
            return True
        else:
            print(f"❌ 归档任务触发失败: {archive_response.status_code}")
            print(f"错误详情: {archive_response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        return False

if __name__ == "__main__":
    success1 = test_lifecycle_policies()
    success2 = test_lifecycle_statistics()
    success3 = test_expired_files_api()
    success4 = test_cleanup_task_trigger()
    success5 = test_archival_task_trigger()
    
    if success1 and success2 and success3 and success4 and success5:
        print("\n🎉 所有文件生命周期管理测试通过！")
        sys.exit(0)
    else:
        print("\n💥 文件生命周期管理测试失败！")
        sys.exit(1)
