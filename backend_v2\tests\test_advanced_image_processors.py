#!/usr/bin/env python3
"""
测试高级图像处理器
验证2.3.3任务的高级图像处理功能
"""
import sys
import os
import numpy as np
import cv2
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_advanced_processor_imports():
    """测试高级处理器导入"""
    print("🧪 测试高级处理器导入...")
    
    try:
        from app.services.image_processors import (
            ImageFusionProcessor, BeautyEnhancementProcessor,
            ImageStitchingProcessor, TextureTransferProcessor,
            validate_image_fusion_parameters, validate_beauty_enhancement_parameters,
            validate_image_stitching_parameters, validate_texture_transfer_parameters
        )
        print("✅ 所有高级处理器导入成功")
        return True
        
    except Exception as e:
        print(f"❌ 高级处理器导入失败: {str(e)}")
        return False

def test_image_fusion_algorithm():
    """测试图像融合算法"""
    print("\n🧪 测试图像融合算法...")
    
    try:
        from app.services.image_processors.image_fusion_processor import ImageFusionProcessor
        from app.services.minio_service import get_minio_service
        from app.core.database import get_db_session
        
        # 创建两个测试图像
        image1 = np.full((100, 100, 3), [255, 0, 0], dtype=np.uint8)  # 红色
        image2 = np.full((100, 100, 3), [0, 0, 255], dtype=np.uint8)  # 蓝色
        
        # 创建处理器
        db = get_db_session()
        minio_service = get_minio_service()
        
        try:
            processor = ImageFusionProcessor(minio_service, db)
            
            # 测试融合算法
            params = {"weight1": 0.6, "weight2": 0.4}
            result = processor.process_image_fusion(image1, image2, params)
            
            if result.shape == image1.shape:
                print("✅ 图像融合算法输出形状正确")
            else:
                print(f"❌ 输出形状错误: {result.shape}")
                return False
            
            # 检查融合效果（应该是红蓝混合）
            mean_color = np.mean(result, axis=(0, 1))
            if mean_color[0] > mean_color[2]:  # 红色分量应该更高
                print("✅ 图像融合权重正确（红色权重更高）")
            else:
                print("❌ 图像融合权重不正确")
                return False
            
            return True
            
        finally:
            db.close()
        
    except Exception as e:
        print(f"❌ 图像融合算法测试失败: {str(e)}")
        return False

def test_beauty_enhancement_algorithm():
    """测试美颜增强算法"""
    print("\n🧪 测试美颜增强算法...")
    
    try:
        from app.services.image_processors.beauty_enhancement_processor import BeautyEnhancementProcessor
        from app.services.minio_service import get_minio_service
        from app.core.database import get_db_session
        
        # 创建测试图像（带一些噪声）
        test_image = np.random.randint(100, 200, (200, 200, 3), dtype=np.uint8)
        
        # 创建处理器
        db = get_db_session()
        minio_service = get_minio_service()
        
        try:
            processor = BeautyEnhancementProcessor(minio_service, db)
            
            # 测试美颜算法
            params = {"smoothing_strength": 0.5, "slimming_strength": 0.3}
            result = processor.process_image(test_image, params)
            
            if result.shape == test_image.shape:
                print("✅ 美颜增强算法输出形状正确")
            else:
                print(f"❌ 输出形状错误: {result.shape}")
                return False
            
            # 检查是否有处理效果（图像应该有变化）
            if not np.array_equal(result, test_image):
                print("✅ 美颜增强算法产生了处理效果")
            else:
                print("❌ 美颜增强算法没有产生效果")
                return False
            
            return True
            
        finally:
            db.close()
        
    except Exception as e:
        print(f"❌ 美颜增强算法测试失败: {str(e)}")
        return False

def test_parameter_validation():
    """测试高级处理器参数验证"""
    print("\n🧪 测试高级处理器参数验证...")
    
    try:
        from app.services.image_processors import (
            validate_image_fusion_parameters,
            validate_beauty_enhancement_parameters,
            validate_image_stitching_parameters,
            validate_texture_transfer_parameters
        )
        
        # 测试图像融合参数
        fusion_params = validate_image_fusion_parameters({"weight1": 0.7, "weight2": 0.3})
        if fusion_params["weight1"] == 0.7 and fusion_params["weight2"] == 0.3:
            print("✅ 图像融合参数验证成功")
        else:
            print("❌ 图像融合参数验证失败")
            return False
        
        # 测试美颜参数
        beauty_params = validate_beauty_enhancement_parameters({
            "smoothing_strength": 0.6, 
            "slimming_strength": 0.4
        })
        if beauty_params["smoothing_strength"] == 0.6:
            print("✅ 美颜增强参数验证成功")
        else:
            print("❌ 美颜增强参数验证失败")
            return False
        
        # 测试拼接参数
        stitching_params = validate_image_stitching_parameters({"mode": "panorama"})
        if stitching_params["mode"] == "panorama":
            print("✅ 图像拼接参数验证成功")
        else:
            print("❌ 图像拼接参数验证失败")
            return False
        
        # 测试纹理迁移参数
        texture_params = validate_texture_transfer_parameters({
            "block_size": 32, 
            "alpha": 0.7
        })
        if texture_params["block_size"] == 32 and texture_params["alpha"] == 0.7:
            print("✅ 纹理迁移参数验证成功")
        else:
            print("❌ 纹理迁移参数验证失败")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 参数验证测试失败: {str(e)}")
        return False

def test_error_handling():
    """测试错误处理"""
    print("\n🧪 测试错误处理...")
    
    try:
        from app.services.image_processors import (
            validate_image_fusion_parameters,
            validate_beauty_enhancement_parameters,
            validate_texture_transfer_parameters
        )
        
        # 测试无效参数
        test_cases = [
            (validate_image_fusion_parameters, {"weight1": -0.5, "weight2": 0.5}, "负权重"),
            (validate_beauty_enhancement_parameters, {"smoothing_strength": 1.5}, "超范围强度"),
            (validate_texture_transfer_parameters, {"block_size": 200}, "超大块大小")
        ]
        
        for validator, params, description in test_cases:
            try:
                validator(params)
                print(f"❌ {description}参数未被拒绝")
                return False
            except ValueError:
                print(f"✅ {description}参数正确被拒绝")
        
        return True
        
    except Exception as e:
        print(f"❌ 错误处理测试失败: {str(e)}")
        return False

def test_task_imports():
    """测试任务导入"""
    print("\n🧪 测试任务导入...")
    
    try:
        from app.tasks.image_tasks import process_image_fusion
        
        # 检查任务是否正确注册
        if hasattr(process_image_fusion, 'name'):
            print(f"✅ 图像融合任务已注册: {process_image_fusion.name}")
        else:
            print("❌ 图像融合任务未正确注册")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 任务导入测试失败: {str(e)}")
        return False

def test_multi_file_architecture():
    """测试多文件输入架构"""
    print("\n🧪 测试多文件输入架构...")
    
    try:
        from app.services.image_processors.image_fusion_processor import ImageFusionProcessor
        from app.services.minio_service import get_minio_service
        from app.core.database import get_db_session
        
        # 创建处理器
        db = get_db_session()
        minio_service = get_minio_service()
        
        try:
            processor = ImageFusionProcessor(minio_service, db)
            
            # 测试单图像接口应该抛出异常
            test_image = np.zeros((50, 50, 3), dtype=np.uint8)
            try:
                processor.process_image(test_image, {})
                print("❌ 单图像接口未正确抛出异常")
                return False
            except NotImplementedError:
                print("✅ 单图像接口正确抛出NotImplementedError")
            
            return True
            
        finally:
            db.close()
        
    except Exception as e:
        print(f"❌ 多文件架构测试失败: {str(e)}")
        return False

if __name__ == "__main__":
    print("🚀 开始高级图像处理器测试...")
    
    success1 = test_advanced_processor_imports()
    success2 = test_image_fusion_algorithm()
    success3 = test_beauty_enhancement_algorithm()
    success4 = test_parameter_validation()
    success5 = test_error_handling()
    success6 = test_task_imports()
    success7 = test_multi_file_architecture()
    
    if success1 and success2 and success3 and success4 and success5 and success6 and success7:
        print("\n🎉 所有高级图像处理器测试通过！")
        print("\n📋 2.3.3任务验证完成:")
        print("✅ 高级处理器导入：所有4个高级处理器正确导入")
        print("✅ 图像融合算法：权重融合和尺寸调整正确")
        print("✅ 美颜增强算法：磨皮和瘦脸效果正常")
        print("✅ 参数验证：所有高级处理器参数验证正常")
        print("✅ 错误处理：无效参数正确被拒绝")
        print("✅ 任务集成：Celery任务正确注册")
        print("✅ 多文件架构：多文件输入接口设计正确")
        print("\n🎯 2.3.3高级图像处理功能迁移完成！")
        sys.exit(0)
    else:
        print("\n💥 高级图像处理器测试失败！")
        sys.exit(1)
