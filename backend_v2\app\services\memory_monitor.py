"""
视频处理内存监控模块
基于后端设计文档的简洁理念，提供基础的内存保护功能
专注于防止OOM，避免过度复杂的动态调整
"""

import psutil
import gc
from typing import Optional
from dataclasses import dataclass
import structlog

logger = structlog.get_logger()


@dataclass
class MemoryUsage:
    """内存使用情况数据类"""
    process_memory_mb: float
    system_memory_percent: float
    system_available_mb: float
    is_warning: bool
    is_critical: bool


@dataclass
class PerformanceConfig:
    """性能配置数据类 - 简化版本，专注核心功能"""
    batch_size: int = 15  # 固定批处理大小，基于旧框架的成功经验
    memory_limit_mb: int = 4096
    warning_threshold: float = 0.8
    progress_update_interval: int = 10


class MemoryMonitor:
    """简化的内存监控类 - 专注于基础的OOM保护"""

    def __init__(self, max_memory_mb: int = 4096, warning_threshold: float = 0.8):
        """
        初始化内存监控器

        Args:
            max_memory_mb: 最大内存使用限制（MB）
            warning_threshold: 警告阈值（0-1之间的比例）
        """
        self.max_memory_mb = max_memory_mb
        self.warning_threshold = warning_threshold
        self.warning_memory_mb = max_memory_mb * warning_threshold
        self.process = psutil.Process()
    
    def get_memory_usage(self) -> MemoryUsage:
        """获取当前内存使用情况"""
        try:
            memory_info = self.process.memory_info()
            memory_mb = memory_info.rss / (1024 * 1024)

            system_memory = psutil.virtual_memory()

            return MemoryUsage(
                process_memory_mb=memory_mb,
                system_memory_percent=system_memory.percent,
                system_available_mb=system_memory.available / (1024 * 1024),
                is_warning=memory_mb > self.warning_memory_mb,
                is_critical=memory_mb > self.max_memory_mb
            )
        except Exception as e:
            logger.error("memory_usage_check_failed", error=str(e))
            return MemoryUsage(
                process_memory_mb=0,
                system_memory_percent=0,
                system_available_mb=0,
                is_warning=False,
                is_critical=False
            )
    
    def check_memory_limit(self) -> bool:
        """检查是否超过内存限制"""
        memory_usage = self.get_memory_usage()

        if memory_usage.is_critical:
            logger.error("memory_usage_critical",
                        memory_mb=memory_usage.process_memory_mb,
                        limit_mb=self.max_memory_mb)
            return False
        elif memory_usage.is_warning:
            logger.warning("memory_usage_warning",
                          memory_mb=memory_usage.process_memory_mb,
                          warning_mb=self.warning_memory_mb)

        return True
    
    def force_garbage_collection(self) -> float:
        """强制垃圾回收"""
        try:
            before_memory = self.get_memory_usage().process_memory_mb

            # 执行垃圾回收
            collected = gc.collect()

            after_memory = self.get_memory_usage().process_memory_mb
            freed_mb = before_memory - after_memory

            logger.info("garbage_collection_completed",
                       collected_objects=collected,
                       freed_mb=freed_mb)

            return freed_mb
        except Exception as e:
            logger.error("garbage_collection_failed", error=str(e))
            return 0


def create_memory_monitor(max_memory_mb: Optional[int] = None) -> MemoryMonitor:
    """创建内存监控器"""
    if max_memory_mb is None:
        # 默认使用系统可用内存的1/4
        available_memory = psutil.virtual_memory().available
        max_memory_mb = min(4096, available_memory // (1024 * 1024) // 4)

    return MemoryMonitor(max_memory_mb)


def create_performance_config() -> PerformanceConfig:
    """创建性能配置"""
    return PerformanceConfig()
