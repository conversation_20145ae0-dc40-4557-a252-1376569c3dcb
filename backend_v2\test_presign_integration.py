#!/usr/bin/env python3
"""
测试预签名URL API的数据库集成
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.api.files import get_presigned_upload_url
from app.core.database import get_db_session
from app.services.minio_service import get_minio_service
from app.crud.crud_file import file as file_crud

def test_presign_database_integration():
    """测试预签名URL生成是否正确保存到数据库"""
    print("🧪 测试预签名URL数据库集成...")
    
    try:
        # 获取数据库会话
        db = get_db_session()
        
        # 获取MinIO服务
        minio_service = get_minio_service()
        
        # 测试参数
        filename = "test_image.jpg"
        file_type = "image/jpeg"
        expires_hours = 2
        
        print(f"📁 测试文件: {filename}")
        print(f"📄 文件类型: {file_type}")
        print(f"⏰ 过期时间: {expires_hours}小时")
        
        # 检查数据库连接
        from app.core.database import check_database_connection
        if not check_database_connection():
            print("❌ 数据库连接失败")
            return False
            
        print("✅ 数据库连接正常")
        
        # 检查MinIO连接
        if not minio_service.check_connection():
            print("❌ MinIO连接失败")
            return False
            
        print("✅ MinIO连接正常")
        
        # 查询修改前的文件数量
        files_before = len(file_crud.get_multi(db=db, limit=1000))
        print(f"📊 修改前文件数量: {files_before}")
        
        # 模拟API调用（需要手动处理依赖注入）
        print("🔄 生成预签名URL...")
        
        # 这里我们需要手动测试核心逻辑
        from app.utils.storage_path import StoragePathManager, StorageType
        from app.schemas.file import FileCreate
        from app.models.file import FileType, FileStatus
        from datetime import datetime, timedelta
        
        # 生成存储路径
        object_name, file_id = StoragePathManager.generate_file_path(
            filename=filename,
            storage_type=StorageType.UPLOAD
        )
        
        print(f"🗂️ 生成的存储路径: {object_name}")
        print(f"🆔 文件ID: {file_id}")
        
        # 生成预签名URL
        expires = timedelta(hours=expires_hours)
        presigned_url = minio_service.generate_presigned_put_url(
            object_name=object_name,
            expires=expires
        )
        
        if not presigned_url:
            print("❌ 预签名URL生成失败")
            return False
            
        print(f"🔗 预签名URL生成成功: {presigned_url[:50]}...")
        
        # 保存文件元数据到数据库
        file_category = StoragePathManager.get_file_category(filename)
        
        # 确定文件类型
        if file_category.value == "images":
            db_file_type = FileType.IMAGE
        elif file_category.value == "videos":
            db_file_type = FileType.VIDEO
        elif file_category.value == "audio":
            db_file_type = FileType.AUDIO
        elif file_category.value == "documents":
            db_file_type = FileType.DOCUMENT
        else:
            db_file_type = FileType.OTHER
        
        # 创建文件记录
        file_create = FileCreate(
            filename=filename,
            original_filename=filename,
            file_type=db_file_type,
            content_type=file_type,
            storage_path=object_name,
            storage_bucket=minio_service.bucket_name,
            file_metadata={
                "presigned_url_generated": True,
                "expires_at": (datetime.now() + expires).isoformat(),
                "upload_method": "presigned_url"
            }
        )
        
        # 保存到数据库
        db_file = file_crud.create(db=db, obj_in=file_create)
        
        print(f"💾 文件记录已保存到数据库，ID: {db_file.id}")
        print(f"📝 文件名: {db_file.filename}")
        print(f"📂 存储路径: {db_file.storage_path}")
        print(f"🏷️ 文件类型: {db_file.file_type}")
        print(f"📊 状态: {db_file.status}")
        
        # 验证数据库记录
        files_after = len(file_crud.get_multi(db=db, limit=1000))
        print(f"📊 修改后文件数量: {files_after}")
        
        if files_after == files_before + 1:
            print("✅ 数据库集成测试成功！")
            return True
        else:
            print("❌ 数据库记录数量不正确")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        if 'db' in locals():
            db.close()

if __name__ == "__main__":
    success = test_presign_database_integration()
    if success:
        print("\n🎉 所有测试通过！")
        sys.exit(0)
    else:
        print("\n💥 测试失败！")
        sys.exit(1)
