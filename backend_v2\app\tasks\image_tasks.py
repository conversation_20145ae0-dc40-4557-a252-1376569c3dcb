"""
图像处理任务
实现各种图像处理的Celery任务
"""

from typing import Dict, Any, List
import structlog
from celery import current_task
from sqlalchemy.orm import Session

from app.core.celery_app import celery_app
from app.core.database import get_db_session
from app.services.minio_service import get_minio_service
from app.services.image_processor_base import ParameterConverter
from app.services.image_processors import (
    create_grayscale_processor, validate_grayscale_parameters,
    create_sharpen_processor, validate_sharpen_parameters,
    create_edge_detection_processor, validate_edge_detection_parameters,
    create_gamma_correction_processor, validate_gamma_correction_parameters,
    create_image_fusion_processor, validate_image_fusion_parameters,
    create_beauty_enhancement_processor, validate_beauty_enhancement_parameters,
    create_image_stitching_processor, validate_image_stitching_parameters,
    create_texture_transfer_processor, validate_texture_transfer_parameters
)
from app.crud.crud_file import file as file_crud

logger = structlog.get_logger()


@celery_app.task(bind=True, name="app.tasks.image_tasks.process_image_sharpen")
def process_image_sharpen(self, file_info: Dict[str, Any], parameters: Dict[str, Any]):
    """图像锐化任务"""
    db = None
    try:
        logger.info("processing_image_sharpen",
                   file_info=file_info,
                   parameters=parameters,
                   task_id=self.request.id)

        # 获取数据库会话和MinIO服务
        db = get_db_session()
        minio_service = get_minio_service()

        # 验证和转换参数
        validated_params = validate_sharpen_parameters(parameters)

        # 获取文件对象
        file_id = file_info.get('file_id') or file_info.get('id')
        if not file_id:
            raise ValueError("Missing file_id in file_info")

        file_obj = file_crud.get(db=db, id=file_id)
        if not file_obj:
            raise ValueError(f"File not found: {file_id}")

        # 创建锐化处理器并执行处理
        with create_sharpen_processor(minio_service, db) as processor:
            result = processor.execute_image_processing(
                file_obj=file_obj,
                parameters=validated_params,
                operation_name="sharpen",
                task_id=self.request.id
            )

        logger.info("image_sharpen_task_completed",
                   file_id=file_id,
                   result_file_id=result.get('output_file_id'),
                   processing_time=result.get('processing_time'),
                   intensity=validated_params.get('intensity'))

        return result

    except Exception as e:
        error_msg = str(e)
        logger.error("image_sharpen_task_failed",
                    file_info=file_info,
                    error=error_msg,
                    task_id=self.request.id)

        # 返回错误结果
        return {
            "success": False,
            "error": error_msg,
            "processing_time": 0
        }
    finally:
        if db:
            db.close()


@celery_app.task(bind=True, name="app.tasks.image_tasks.process_image_grayscale")
def process_image_grayscale(self, file_info: Dict[str, Any], parameters: Dict[str, Any]):
    """图像灰度转换任务"""
    db = None
    try:
        logger.info("processing_image_grayscale",
                   file_info=file_info,
                   parameters=parameters,
                   task_id=self.request.id)

        # 获取数据库会话和MinIO服务
        db = get_db_session()
        minio_service = get_minio_service()

        # 验证和转换参数
        validated_params = validate_grayscale_parameters(parameters)

        # 获取文件对象
        file_id = file_info.get('file_id') or file_info.get('id')
        if not file_id:
            raise ValueError("Missing file_id in file_info")

        file_obj = file_crud.get(db=db, id=file_id)
        if not file_obj:
            raise ValueError(f"File not found: {file_id}")

        # 创建灰度处理器并执行处理
        with create_grayscale_processor(minio_service, db) as processor:
            result = processor.execute_image_processing(
                file_obj=file_obj,
                parameters=validated_params,
                operation_name="grayscale",
                task_id=self.request.id
            )

        logger.info("image_grayscale_task_completed",
                   file_id=file_id,
                   result_file_id=result.get('output_file_id'),
                   processing_time=result.get('processing_time'))

        return result

    except Exception as e:
        error_msg = str(e)
        logger.error("image_grayscale_task_failed",
                    file_info=file_info,
                    error=error_msg,
                    task_id=self.request.id)

        # 返回错误结果
        return {
            "success": False,
            "error": error_msg,
            "processing_time": 0
        }
    finally:
        if db:
            db.close()


@celery_app.task(bind=True, name="app.tasks.image_tasks.process_image_edge_detection")
def process_image_edge_detection(self, file_info: Dict[str, Any], parameters: Dict[str, Any]):
    """图像边缘检测任务"""
    db = None
    try:
        logger.info("processing_image_edge_detection",
                   file_info=file_info,
                   parameters=parameters,
                   task_id=self.request.id)

        # 获取数据库会话和MinIO服务
        db = get_db_session()
        minio_service = get_minio_service()

        # 验证和转换参数
        validated_params = validate_edge_detection_parameters(parameters)

        # 获取文件对象
        file_id = file_info.get('file_id') or file_info.get('id')
        if not file_id:
            raise ValueError("Missing file_id in file_info")

        file_obj = file_crud.get(db=db, id=file_id)
        if not file_obj:
            raise ValueError(f"File not found: {file_id}")

        # 创建边缘检测处理器并执行处理
        with create_edge_detection_processor(minio_service, db) as processor:
            result = processor.execute_image_processing(
                file_obj=file_obj,
                parameters=validated_params,
                operation_name="edge_detection",
                task_id=self.request.id
            )

        logger.info("image_edge_detection_task_completed",
                   file_id=file_id,
                   result_file_id=result.get('output_file_id'),
                   processing_time=result.get('processing_time'),
                   low_threshold=validated_params.get('low_threshold'),
                   high_threshold=validated_params.get('high_threshold'))

        return result

    except Exception as e:
        error_msg = str(e)
        logger.error("image_edge_detection_task_failed",
                    file_info=file_info,
                    error=error_msg,
                    task_id=self.request.id)

        # 返回错误结果
        return {
            "success": False,
            "error": error_msg,
            "processing_time": 0
        }
    finally:
        if db:
            db.close()


@celery_app.task(bind=True, name="app.tasks.image_tasks.process_image_gamma_correction")
def process_image_gamma_correction(self, file_info: Dict[str, Any], parameters: Dict[str, Any]):
    """图像伽马校正任务"""
    db = None
    try:
        logger.info("processing_image_gamma_correction",
                   file_info=file_info,
                   parameters=parameters,
                   task_id=self.request.id)

        # 获取数据库会话和MinIO服务
        db = get_db_session()
        minio_service = get_minio_service()

        # 验证和转换参数
        validated_params = validate_gamma_correction_parameters(parameters)

        # 获取文件对象
        file_id = file_info.get('file_id') or file_info.get('id')
        if not file_id:
            raise ValueError("Missing file_id in file_info")

        file_obj = file_crud.get(db=db, id=file_id)
        if not file_obj:
            raise ValueError(f"File not found: {file_id}")

        # 创建伽马校正处理器并执行处理
        with create_gamma_correction_processor(minio_service, db) as processor:
            result = processor.execute_image_processing(
                file_obj=file_obj,
                parameters=validated_params,
                operation_name="gamma_correction",
                task_id=self.request.id
            )

        logger.info("image_gamma_correction_task_completed",
                   file_id=file_id,
                   result_file_id=result.get('output_file_id'),
                   processing_time=result.get('processing_time'),
                   gamma=validated_params.get('gamma'))

        return result

    except Exception as e:
        error_msg = str(e)
        logger.error("image_gamma_correction_task_failed",
                    file_info=file_info,
                    error=error_msg,
                    task_id=self.request.id)

        # 返回错误结果
        return {
            "success": False,
            "error": error_msg,
            "processing_time": 0
        }
    finally:
        if db:
            db.close()


@celery_app.task(bind=True, name="app.tasks.image_tasks.process_image_fusion")
def process_image_fusion(self, file_info_list: List[Dict[str, Any]], parameters: Dict[str, Any]):
    """图像融合任务 - 处理双文件输入"""
    db = None
    try:
        logger.info("processing_image_fusion",
                   file_info_list=file_info_list,
                   parameters=parameters,
                   task_id=self.request.id)

        if len(file_info_list) != 2:
            raise ValueError(f"Image fusion requires exactly 2 files, got {len(file_info_list)}")

        # 获取数据库会话和MinIO服务
        db = get_db_session()
        minio_service = get_minio_service()

        # 验证和转换参数
        validated_params = validate_image_fusion_parameters(parameters)

        # 获取文件对象
        file_objects = []
        for file_info in file_info_list:
            file_id = file_info.get('file_id') or file_info.get('id')
            if not file_id:
                raise ValueError("Missing file_id in file_info")

            file_obj = file_crud.get(db=db, id=file_id)
            if not file_obj:
                raise ValueError(f"File not found: {file_id}")

            file_objects.append(file_obj)

        # 创建图像融合处理器并执行处理
        with create_image_fusion_processor(minio_service, db) as processor:
            result = processor.process_multiple_images(
                file_objects=file_objects,
                parameters=validated_params,
                operation_name="image_fusion",
                task_id=self.request.id
            )

        logger.info("image_fusion_task_completed",
                   file_ids=[f.id for f in file_objects],
                   result_file_id=result.get('output_file_id'),
                   processing_time=result.get('processing_time'))

        return result

    except Exception as e:
        error_msg = str(e)
        logger.error("image_fusion_task_failed",
                    file_info_list=file_info_list,
                    error=error_msg,
                    task_id=self.request.id)

        # 返回错误结果
        return {
            "success": False,
            "error": error_msg,
            "processing_time": 0
        }
    finally:
        if db:
            db.close()


@celery_app.task(bind=True, name="app.tasks.image_tasks.process_image_stitching")
def process_image_stitching(self, file_info: Dict[str, Any], parameters: Dict[str, Any]):
    """图像拼接任务"""
    logger.info("processing_image_stitching", file_info=file_info, parameters=parameters)

    # TODO: 实现真实的图像拼接逻辑

    return {
        "success": True,
        "output_path": f"processed/stitched_{file_info['filename']}",
        "processing_time": 3.0
    }


@celery_app.task(bind=True, name="app.tasks.image_tasks.process_beauty_enhancement")
def process_beauty_enhancement(self, file_info: Dict[str, Any], parameters: Dict[str, Any]):
    """美颜增强任务"""
    logger.info("processing_beauty_enhancement", file_info=file_info, parameters=parameters)

    # TODO: 实现真实的美颜增强逻辑

    return {
        "success": True,
        "output_path": f"processed/beauty_enhanced_{file_info['filename']}",
        "processing_time": 2.5
    }


@celery_app.task(bind=True, name="app.tasks.image_tasks.process_texture_transfer")
def process_texture_transfer(self, file_info: Dict[str, Any], parameters: Dict[str, Any]):
    """纹理转换任务"""
    logger.info("processing_texture_transfer", file_info=file_info, parameters=parameters)

    # TODO: 实现真实的纹理转换逻辑

    return {
        "success": True,
        "output_path": f"processed/texture_transferred_{file_info['filename']}",
        "processing_time": 4.0
    }
